#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试metadata修复是否有效
验证新的metadata结构是否包含所有必需的信息
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from table_render.utils.metadata_optimizer import metadata_optimizer


def test_metadata_completeness():
    """测试metadata完整性"""
    print("测试metadata完整性...")
    
    # 模拟完整的resolved_params
    resolved_params = {
        "structure": {
            "header_rows": 2,
            "body_rows": 8,
            "cols": 6,
            "merge_probability": 0.12,
            "max_row_span": 5,
            "max_col_span": 2
        },
        "content": {
            "source_type": "csv"
        },
        "style": {
            "overflow_strategy": "wrap",
            "border_mode": {"mode": "semi"},
            "zebra_stripes": 0.1
        },
        "postprocessing": {
            "table_blending": {
                "enable_transparency": True,
                "default_color_transparency": 0.0,
                "meaningful_color_transparency": 0.7
            }
        },
        "performance": {
            "enable_parallel": True,
            "max_workers": 16,
            "enable_turbo_jpeg": True,
            "turbo_jpeg_quality": 100,
            "turbo_jpeg_format": "png",
            "css_stability": {
                "enable_validation": True
            }
        },
        "seed": 66
    }
    
    # 模拟完整的collected_metadata
    collected_metadata = {
        # 结构选择
        "selected_header_rows": 2,
        "selected_header_rows_range": [2, 2],
        "header_rows_range_probability": 0.3,
        
        "selected_body_rows": 8,
        "selected_body_rows_range": [6, 10],
        "body_rows_range_probability": 0.25,
        
        "selected_cols": 6,
        "selected_cols_range": [6, 10],
        "cols_range_probability": 0.45,
        
        "selected_merge_probability": 0.12,
        "selected_merge_probability_range": [0.1, 0.15],
        "merge_probability_range_probability": 0.3,
        
        "selected_max_row_span": 5,
        "max_row_span_option_probability": 0.7,
        
        "selected_max_col_span": 2,
        "max_col_span_option_probability": 0.29,
        
        # 样式选择
        "selected_font_family": "Arial",
        "font_family_option_probability": 0.4,
        
        "selected_font_size": 14,
        "selected_font_size_range": [10, 15],
        "font_size_range_probability": 0.4,
        
        "selected_horizontal_align": "center",
        "horizontal_align_option_probability": 0.6,
        
        "selected_vertical_align": "middle",
        "vertical_align_option_probability": 0.5,
        
        "selected_padding": 5,
        "selected_padding_range": [4, 6],
        "padding_range_probability": 0.4,
        
        # 文件选择
        "selected_csv_file": "assets/corpus/nl2sql_train/test.csv",
        "selected_csv_directory": "assets/corpus/nl2sql_train/",
        "csv_directory_probability": 0.5,
        
        "selected_font_file": "/path/to/font/arial.ttf",
        "selected_font_directory": "/path/to/font/common/",
        "font_directory_probability": 0.8,
        
        "selected_background_file": "/path/to/bg.jpg",
        "selected_background_directory": "/path/to/bg/",
        "background_directory_probability": 0.5,
        
        # 后处理选择
        "selected_margin_control": 75,
        "selected_margin_control_range": [60, 80],
        "margin_control_range_probability": 0.3,
        
        # CSS渲染信息
        "css_table_left": 2063,
        "css_table_top": 1245,
        "css_background_width": 4800,
        "css_background_height": 3200,
        "css_crop_width": 1920,
        "css_crop_height": 1080,
        "css_bg_offset_x": -1440,
        "css_bg_offset_y": -1060,
        "apply_background": True,
        "apply_perspective": False,
        "max_scale_factor": 3.0,
        "content_area_shrink_ratio": 0.1,
        
        # 继承变化信息
        "inheritance_font_family_changed": False,
        "inheritance_font_size_changed": True,
        "inheritance_alignment_changed": False,
        "inheritance_padding_changed": True,
        "inheritance_text_color_changed": False,
        "inheritance_background_color_changed": False
    }
    
    # 模拟CSV采样信息
    csv_sampling_info = {
        "source_file": "assets/corpus/nl2sql_train/test.csv",
        "selected_columns": [0, 2, 4, 7, 9, 11],
        "selected_rows": [1, 3, 5, 8, 12, 15, 18, 22],
        "csv_structure": {
            "total_columns": 15,
            "total_data_rows": 25
        },
        "sampling_mode": "random"
    }
    
    # 测试优化器
    optimized_metadata = metadata_optimizer.optimize_metadata(
        resolved_params=resolved_params,
        collected_metadata=collected_metadata,
        csv_sampling_info=csv_sampling_info,
        sample_seed=1234567890,
        sample_index=0,
        generation_timestamp=1722345678.123,
        turbo_jpeg_used=True
    )
    
    # 验证所有必需的字段
    required_sections = [
        "sample_seed", "sample_index", "generation_timestamp", "turbo_jpeg_used",
        "structure_choices", "style_choices", "file_choices", 
        "postprocessing_choices", "performance_info", "inheritance_applied",
        "css_render_info", "csv_sampling_info", "resolved_key_params"
    ]
    
    print("验证必需的顶级字段...")
    for section in required_sections:
        if section not in optimized_metadata:
            print(f"❌ 缺少字段: {section}")
            return False
        else:
            print(f"✅ 字段存在: {section}")
    
    # 验证结构选择的完整性
    print("\n验证结构选择...")
    structure_choices = optimized_metadata["structure_choices"]
    required_structure = ["header_rows", "body_rows", "cols", "merge_probability", "max_row_span", "max_col_span"]
    for field in required_structure:
        if field not in structure_choices:
            print(f"❌ 结构选择缺少: {field}")
            return False
        else:
            print(f"✅ 结构选择包含: {field}")
    
    # 验证样式选择的完整性
    print("\n验证样式选择...")
    style_choices = optimized_metadata["style_choices"]
    required_style = ["font_family", "font_size", "horizontal_align", "vertical_align", "padding"]
    for field in required_style:
        if field not in style_choices:
            print(f"❌ 样式选择缺少: {field}")
            return False
        else:
            print(f"✅ 样式选择包含: {field}")
    
    # 验证文件选择的完整性
    print("\n验证文件选择...")
    file_choices = optimized_metadata["file_choices"]
    required_files = ["csv", "font", "background"]
    for field in required_files:
        if field not in file_choices:
            print(f"❌ 文件选择缺少: {field}")
            return False
        else:
            print(f"✅ 文件选择包含: {field}")
    
    # 验证性能信息的完整性
    print("\n验证性能信息...")
    performance_info = optimized_metadata["performance_info"]
    required_performance = ["parallel_enabled", "max_workers", "turbo_jpeg_enabled", "turbo_jpeg_quality", "turbo_jpeg_format", "css_stability_enabled"]
    for field in required_performance:
        if field not in performance_info:
            print(f"❌ 性能信息缺少: {field}")
            return False
        else:
            print(f"✅ 性能信息包含: {field}")
    
    # 验证后处理选择的完整性
    print("\n验证后处理选择...")
    postprocessing_choices = optimized_metadata["postprocessing_choices"]
    required_postprocessing = ["margin_control", "degradation_effects_applied", "perspective_applied", "background_applied", "table_blending_enabled", "transparency_settings"]
    for field in required_postprocessing:
        if field not in postprocessing_choices:
            print(f"❌ 后处理选择缺少: {field}")
            return False
        else:
            print(f"✅ 后处理选择包含: {field}")
    
    # 验证resolved_key_params的完整性
    print("\n验证关键参数...")
    resolved_key_params = optimized_metadata["resolved_key_params"]
    required_key_params = ["table_size", "content_source", "overflow_strategy", "border_mode", "zebra_stripes_enabled", "transparency_enabled", "random_seed"]
    for field in required_key_params:
        if field not in resolved_key_params:
            print(f"❌ 关键参数缺少: {field}")
            return False
        else:
            print(f"✅ 关键参数包含: {field}")
    
    print("\n🎉 所有验证通过！metadata结构完整！")
    
    # 保存测试结果
    output_path = "test_metadata_complete.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(optimized_metadata, f, indent=2, ensure_ascii=False)
    print(f"完整的metadata已保存到: {output_path}")
    
    return True


if __name__ == "__main__":
    success = test_metadata_completeness()
    sys.exit(0 if success else 1)
