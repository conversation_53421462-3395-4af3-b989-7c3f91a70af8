# TableRender V5.3 详细Metadata收集功能

## 功能概述

TableRender V5.3 新增了详细的metadata收集功能，记录生成过程中的所有关键选择信息，便于调试和分析。

## 收集的信息类型

### 1. 文件夹选择详情

记录从多个目录中选择文件的详细信息：

#### 背景图选择
```json
{
  "selected_background_file": "/path/to/backgrounds/dir2/image.jpg",
  "selected_background_directory": "/path/to/backgrounds/dir2/",
  "background_directory_probability": 0.3,
  "background_all_directories": ["/path/dir1/", "/path/dir2/", "/path/dir3/"],
  "background_all_directory_probabilities": [0.5, 0.3, 0.2]
}
```

#### 字体选择
```json
{
  "selected_font_file": "/path/to/fonts/common/arial.ttf",
  "selected_font_directory": "/path/to/fonts/common/",
  "font_directory_probability": 0.8,
  "font_all_directories": ["/path/fonts/common/", "/path/fonts/rare/"],
  "font_all_directory_probabilities": [0.8, 0.2]
}
```

#### CSV文件选择
```json
{
  "selected_csv_file": "/path/to/data/nl2sql_train/file.csv",
  "selected_csv_directory": "/path/to/data/nl2sql_train/",
  "csv_directory_probability": 0.6,
  "csv_all_directories": ["/path/nl2sql_train/", "/path/wikisql_train/"],
  "csv_all_directory_probabilities": [0.6, 0.4]
}
```

### 2. 概率区间选择详情

记录从概率化范围中选择数值的详细信息：

#### 表格结构选择
```json
{
  "selected_body_rows": 8,
  "selected_body_rows_range": [6, 10],
  "body_rows_range_probability": 0.25,
  "body_rows_all_ranges": [[3, 5], [6, 10], [11, 15]],
  "body_rows_all_range_probabilities": [0.4, 0.25, 0.35],

  "selected_cols": 5,
  "selected_cols_range": [4, 6],
  "cols_range_probability": 0.5,

  "selected_header_rows": 1,
  "selected_header_rows_range": [1, 1],
  "header_rows_range_probability": 1.0
}
```

#### 字体和样式选择
```json
{
  "selected_font_size": 14,
  "selected_font_size_range": [12, 16],
  "font_size_range_probability": 0.4,

  "selected_padding": 8,
  "selected_padding_range": [5, 10],
  "padding_range_probability": 0.3,

  "selected_font_bold_probability": 0.2,
  "selected_font_italic_probability": 0.1
}
```

#### 边距控制选择
```json
{
  "selected_margin_control": 75,
  "selected_margin_control_range": [60, 80],
  "margin_control_range_probability": 0.5,
  "margin_control_all_ranges": [[30, 60], [60, 80], [80, 100]],
  "margin_control_all_range_probabilities": [0.2, 0.5, 0.3]
}
```

### 3. 关键中间值（用于debug）

记录生成过程中的关键计算结果：

#### CSS渲染参数
```json
{
  "css_table_left": 2063,
  "css_table_top": 6238,
  "css_background_width": 5792,
  "css_background_height": 14336,
  "css_crop_width": 5792,
  "css_crop_height": 14336,
  "css_bg_offset_x": 0,
  "css_bg_offset_y": 0
}
```

#### 背景和透视参数
```json
{
  "apply_background": true,
  "css_background_applied": true,
  "apply_perspective": false,
  "perspective_offset_ratio": 0.0,
  "content_area_shrink_ratio": 0.9,
  "max_scale_factor": 3.0
}
```

## 实现架构

### 1. 全局收集器

使用线程安全的单例模式 `MetadataCollector`：

```python
from .utils.metadata_collector import metadata_collector

# 记录文件选择
metadata_collector.record_file_selection(
    category="background",
    selected_file="/path/to/file.jpg",
    selected_directory="/path/to/dir/",
    directory_probability=0.3
)

# 记录范围选择
metadata_collector.record_range_selection(
    category="body_rows",
    selected_value=8,
    selected_range=[6, 10],
    range_probability=0.25
)

# 记录中间值
metadata_collector.record_intermediate_value("css_table_left", 2063)
```

### 2. 收集点分布

#### Resolver中的收集点
- 字体目录和文件选择
- 背景图目录和文件选择
- 表格结构参数选择（行数、列数、表头行数）
- 字体大小、padding等样式参数选择
- CSS渲染参数记录

#### CSV工具中的收集点
- CSV文件目录和文件选择

#### MainGenerator中的收集点
- margin_control边距选择

### 3. 线程安全性

- 使用 `threading.local()` 确保每个线程有独立的metadata存储
- 在每个样本开始时自动清空收集器
- 在样本完成时自动合并到最终metadata中

## 使用方式

### 1. 自动收集

无需额外配置，所有选择信息会自动收集到metadata中。

### 2. 查看详细信息

生成的 `*_meta.json` 文件中会包含所有详细的选择信息：

```json
{
  "resolved_params": { ... },
  "original_config": { ... },
  "sample_seed": 12345,
  "sample_index": 0,
  
  // V5.3新增：详细选择信息
  "selected_background_file": "/path/to/bg.jpg",
  "selected_background_directory": "/path/to/bg/dir/",
  "background_directory_probability": 0.3,
  
  "selected_body_rows": 8,
  "selected_body_rows_range": [6, 10],
  "body_rows_range_probability": 0.25,
  
  "css_table_left": 2063,
  "css_table_top": 6238,
  "css_background_width": 5792,
  "css_background_height": 14336,
  
  // ... 更多详细信息
}
```

## 调试优势

### 1. 问题定位

当生成的图像出现问题时，可以通过metadata快速定位：

- **表格位置异常**：查看 `css_table_left`, `css_table_top`
- **背景图问题**：查看 `selected_background_file`, `css_background_width`
- **表格结构问题**：查看 `selected_body_rows`, `selected_cols`
- **样式问题**：查看字体、padding、边距等选择信息

### 2. 配置验证

验证配置文件中的概率分布是否按预期工作：

- 检查各个目录的实际选择频率
- 验证概率区间的选择分布
- 分析参数组合的合理性

### 3. 重现问题

使用 `sample_seed` 和详细的选择信息，可以精确重现任何问题样本。

## 性能影响

### 1. 内存开销

- 每个样本增加约1-2KB的metadata信息
- 使用线程本地存储，不会累积

### 2. 计算开销

- 收集操作都是简单的字典操作，开销极小
- 不影响主要的生成流程

### 3. 存储开销

- metadata文件大小增加约50-100%
- 但相对于图像文件，仍然很小

## 向后兼容性

- 现有的metadata结构完全保留
- 新增的详细信息以平铺方式添加
- 不影响现有的分析脚本

---

**TableRender V5.3 - 详细Metadata收集，让每个选择都可追踪**
