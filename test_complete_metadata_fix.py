#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试metadata修复
验证所有遗漏的信息是否都被正确修复
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_complete_metadata_fix():
    """测试完整的metadata修复"""
    print("测试完整的metadata修复...")
    
    # 测试1: 验证ResolvedParams是否包含performance字段
    print("\n1. 测试ResolvedParams结构...")
    try:
        from table_render.config import ResolvedParams, ResolvedPerformanceParams
        print("✅ ResolvedPerformanceParams类导入成功")
        
        # 检查ResolvedParams是否有performance字段
        import inspect
        fields = [field for field in inspect.signature(ResolvedParams).parameters.keys()]
        if 'performance' in fields:
            print("✅ ResolvedParams包含performance字段")
        else:
            print("❌ ResolvedParams缺少performance字段")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试2: 验证resolver是否能正确解析performance参数
    print("\n2. 测试resolver性能参数解析...")
    try:
        from table_render.resolver import Resolver
        from table_render.config import RenderConfig, PerformanceConfig
        
        # 创建包含performance配置的config
        performance_config = PerformanceConfig(
            enable_parallel=True,
            max_workers=16,
            enable_turbo_jpeg=True,
            turbo_jpeg_quality=100,
            turbo_jpeg_format="png"
        )
        
        # 创建最小的config用于测试
        from table_render.config import StructureConfig
        structure_config = StructureConfig(
            body_rows=5,
            cols=3,
            header_rows=1
        )
        
        config = RenderConfig(
            structure=structure_config,
            performance=performance_config
        )
        
        resolver = Resolver()
        resolved_params = resolver.resolve(config, seed=12345)
        
        if hasattr(resolved_params, 'performance') and resolved_params.performance is not None:
            print("✅ resolver正确解析了performance参数")
            perf = resolved_params.performance
            print(f"  - parallel_enabled: {perf.enable_parallel}")
            print(f"  - max_workers: {perf.max_workers}")
            print(f"  - turbo_jpeg_enabled: {perf.enable_turbo_jpeg}")
        else:
            print("❌ resolver未能解析performance参数")
            return False
            
    except Exception as e:
        print(f"❌ resolver测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试3: 验证metadata_optimizer是否能正确提取performance信息
    print("\n3. 测试metadata_optimizer性能信息提取...")
    try:
        from table_render.utils.metadata_optimizer import metadata_optimizer
        
        # 模拟resolved_params字典
        resolved_params_dict = resolved_params.dict()
        
        # 模拟collected_metadata
        collected_metadata = {
            "selected_font_family": "Arial",
            "font_family_option_probability": 0.4,
            "selected_font_size": 14,
            "font_size_range_probability": 0.5,
            "selected_horizontal_align": "center",
            "horizontal_align_option_probability": 0.6,
            "selected_font_file": "/path/to/arial.ttf",
            "selected_font_directory": "/path/to/fonts/",
            "font_directory_probability": 0.8
        }
        
        # 测试优化器
        optimized = metadata_optimizer.optimize_metadata(
            resolved_params=resolved_params_dict,
            collected_metadata=collected_metadata,
            csv_sampling_info=None,
            sample_seed=123456,
            sample_index=0,
            generation_timestamp=1234567890.0,
            turbo_jpeg_used=True
        )
        
        # 验证performance_info
        performance_info = optimized.get("performance_info", {})
        if performance_info:
            print("✅ metadata_optimizer正确提取了performance信息")
            print(f"  - parallel_enabled: {performance_info.get('parallel_enabled')}")
            print(f"  - max_workers: {performance_info.get('max_workers')}")
            print(f"  - turbo_jpeg_enabled: {performance_info.get('turbo_jpeg_enabled')}")
        else:
            print("❌ metadata_optimizer未能提取performance信息")
            return False
        
        # 验证style_choices
        style_choices = optimized.get("style_choices", {})
        if style_choices:
            print("✅ metadata_optimizer正确提取了style信息")
            print(f"  - font_family: {style_choices.get('font_family')}")
            print(f"  - font_size: {style_choices.get('font_size')}")
            print(f"  - horizontal_align: {style_choices.get('horizontal_align')}")
        else:
            print("❌ metadata_optimizer未能提取style信息")
            return False
        
        # 验证file_choices
        file_choices = optimized.get("file_choices", {})
        if "font" in file_choices:
            print("✅ metadata_optimizer正确提取了font文件信息")
            print(f"  - font file: {file_choices['font'].get('file')}")
            print(f"  - font directory: {file_choices['font'].get('directory')}")
        else:
            print("❌ metadata_optimizer未能提取font文件信息")
            return False
            
    except Exception as e:
        print(f"❌ metadata_optimizer测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试4: 验证完整的metadata结构
    print("\n4. 验证完整的metadata结构...")
    
    # 检查所有必需的顶级字段
    required_sections = [
        "sample_seed", "sample_index", "generation_timestamp", "turbo_jpeg_used",
        "structure_choices", "style_choices", "file_choices", 
        "postprocessing_choices", "performance_info", "inheritance_applied",
        "css_render_info", "resolved_key_params"
    ]
    
    missing_sections = []
    for section in required_sections:
        if section not in optimized:
            missing_sections.append(section)
    
    if missing_sections:
        print(f"❌ 缺少顶级字段: {missing_sections}")
        return False
    else:
        print("✅ 所有必需的顶级字段都存在")
    
    # 检查style_choices的完整性
    required_style_fields = ["font_family", "font_size", "horizontal_align"]
    style_choices = optimized["style_choices"]
    missing_style_fields = []
    for field in required_style_fields:
        if field not in style_choices:
            missing_style_fields.append(field)
    
    if missing_style_fields:
        print(f"❌ style_choices缺少字段: {missing_style_fields}")
        return False
    else:
        print("✅ style_choices包含所有必需字段")
    
    # 检查performance_info的完整性
    required_perf_fields = ["parallel_enabled", "max_workers", "turbo_jpeg_enabled"]
    performance_info = optimized["performance_info"]
    missing_perf_fields = []
    for field in required_perf_fields:
        if field not in performance_info:
            missing_perf_fields.append(field)
    
    if missing_perf_fields:
        print(f"❌ performance_info缺少字段: {missing_perf_fields}")
        return False
    else:
        print("✅ performance_info包含所有必需字段")
    
    # 保存完整的测试结果
    output_path = "test_complete_metadata_result.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(optimized, f, indent=2, ensure_ascii=False)
    print(f"\n完整的测试结果已保存到: {output_path}")
    
    print("\n🎉 所有测试通过！metadata修复完成！")
    return True


if __name__ == "__main__":
    success = test_complete_metadata_fix()
    sys.exit(0 if success else 1)
