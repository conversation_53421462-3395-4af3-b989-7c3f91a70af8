"""
HTML渲染器

使用Playwright将表格模型渲染为图像并提取标注。
"""

import asyncio
import logging
import threading
import http.server
import socketserver
import os
import time
import json
from pathlib import Path
from typing import Dict, Tuple, Any, Optional
from PIL import Image, ImageDraw
import io

from playwright.async_api import async_playwright, Browser, Page

from ..models import TableModel
from ..utils.performance_profiler import profile_stage


class HtmlRenderer:
    """
    HTML渲染器类

    负责将TableModel转换为HTML，并使用无头浏览器渲染为图像。
    V5.2修复：正确管理Playwright实例生命周期，避免并行渲染时的资源竞争。
    """

    def __init__(self, browser: Browser, playwright_instance, debug_mode: bool = False, debug_output_dir: Optional[str] = None):
        """
        初始化HTML渲染器

        Args:
            browser: Playwright浏览器实例
            playwright_instance: Playwright实例（用于正确清理）
            debug_mode: 是否启用调试模式
            debug_output_dir: 调试输出目录
        """
        self.browser = browser
        self.playwright_instance = playwright_instance  # V5.2新增：保存Playwright实例引用
        self.logger = logging.getLogger(__name__)
        self.http_server = None
        self.http_thread = None
        self.server_port = None
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir
        self.debug_stage_counter = 0

        if self.debug_mode and self.debug_output_dir:
            # 确保调试输出目录存在
            Path(self.debug_output_dir).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"HtmlRenderer调试模式已启用，输出目录: {self.debug_output_dir}")

    def _save_debug_stage(self, stage_name: str, image_bytes: bytes, annotations: Optional[Dict[str, Any]],
                         additional_info: Optional[Dict[str, Any]] = None):
        """
        保存调试阶段的图像和标注

        Args:
            stage_name: 阶段名称
            image_bytes: 图像字节数据
            annotations: 标注数据
            additional_info: 额外信息
        """
        if not self.debug_mode or not self.debug_output_dir:
            return

        self.debug_stage_counter += 1
        stage_prefix = f"stage{self.debug_stage_counter:02d}_{stage_name}"

        # 保存图像
        image_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.png")
        with open(image_path, 'wb') as f:
            f.write(image_bytes)
        self.logger.info(f"调试: 保存图像 {image_path}")

        # 保存标注
        if annotations is not None:
            annotation_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.json")
            debug_data = {
                "stage": stage_name,
                "annotations": annotations
            }
            if additional_info:
                debug_data.update(additional_info)

            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"调试: 保存标注 {annotation_path}")

        # 生成可视化图像（带标注框）
        if annotations and annotations.get('cells'):
            try:
                image = Image.open(io.BytesIO(image_bytes))
                vis_image = self._create_visualization_image(image, annotations)
                vis_path = os.path.join(self.debug_output_dir, f"{stage_prefix}_with_boxes.png")
                vis_image.save(vis_path)
                self.logger.info(f"调试: 保存可视化图像 {vis_path}")
            except Exception as e:
                self.logger.warning(f"生成可视化图像失败: {e}")

    def _create_visualization_image(self, image: Image.Image, annotations: Dict[str, Any]) -> Image.Image:
        """
        创建带标注框的可视化图像

        Args:
            image: 原始图像
            annotations: 标注数据

        Returns:
            带标注框的图像
        """
        vis_image = image.copy()
        draw = ImageDraw.Draw(vis_image)

        # 定义颜色
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, cell in enumerate(annotations.get('cells', [])):
            if 'bbox' not in cell:
                continue

            bbox = cell['bbox']
            color = colors[i % len(colors)]

            if isinstance(bbox, dict) and all(key in bbox for key in ['p1', 'p2', 'p3', 'p4']):
                # 四角点格式
                points = [tuple(bbox['p1']), tuple(bbox['p2']), tuple(bbox['p3']), tuple(bbox['p4'])]
                # 绘制多边形
                draw.polygon(points, outline=color, width=2)
                # 标记单元格编号
                draw.text(bbox['p1'], str(i), fill=color)
            elif isinstance(bbox, list) and len(bbox) >= 4:
                # 矩形格式 [x_min, y_min, x_max, y_max]
                x_min, y_min, x_max, y_max = bbox[:4]
                draw.rectangle([x_min, y_min, x_max, y_max], outline=color, width=2)
                # 标记单元格编号
                draw.text((x_min, y_min), str(i), fill=color)

        return vis_image

    def _calculate_coordinate_changes(self, original_annotations, adjusted_annotations):
        """
        计算坐标调整前后的变化

        Args:
            original_annotations: 原始标注
            adjusted_annotations: 调整后标注

        Returns:
            坐标变化统计
        """
        if not original_annotations or not adjusted_annotations:
            return None

        original_cells = original_annotations.get('cells', [])
        adjusted_cells = adjusted_annotations.get('cells', [])

        if len(original_cells) != len(adjusted_cells):
            return {"error": "单元格数量不匹配"}

        changes = []
        for i, (orig_cell, adj_cell) in enumerate(zip(original_cells, adjusted_cells)):
            if 'bbox' in orig_cell and 'bbox' in adj_cell:
                orig_bbox = orig_cell['bbox']
                adj_bbox = adj_cell['bbox']

                if len(orig_bbox) >= 4 and len(adj_bbox) >= 4:
                    change = {
                        "cell_index": i,
                        "original_bbox": orig_bbox[:4],
                        "adjusted_bbox": adj_bbox[:4],
                        "x_offset": adj_bbox[0] - orig_bbox[0],
                        "y_offset": adj_bbox[1] - orig_bbox[1]
                    }
                    changes.append(change)

        # 计算统计信息
        if changes:
            x_offsets = [c["x_offset"] for c in changes]
            y_offsets = [c["y_offset"] for c in changes]

            return {
                "changes": changes,
                "statistics": {
                    "total_cells": len(changes),
                    "x_offset_min": min(x_offsets),
                    "x_offset_max": max(x_offsets),
                    "x_offset_avg": sum(x_offsets) / len(x_offsets),
                    "y_offset_min": min(y_offsets),
                    "y_offset_max": max(y_offsets),
                    "y_offset_avg": sum(y_offsets) / len(y_offsets),
                    "consistent_offset": len(set(x_offsets)) == 1 and len(set(y_offsets)) == 1
                }
            }

        return {"changes": [], "statistics": None}

    def _get_actual_table_offset(self, annotations, background_params):
        """
        获取实际使用的表格偏移值

        Args:
            annotations: 标注数据
            background_params: 背景参数

        Returns:
            (actual_offset_x, actual_offset_y): 实际使用的偏移值
        """
        if annotations and 'table_bbox' in annotations and annotations['table_bbox']:
            # 使用JavaScript获取的实际位置
            table_bbox = annotations['table_bbox']
            return table_bbox[0], table_bbox[1]
        else:
            # 回退到CSS设置
            return (background_params.css_table_left or 0, background_params.css_table_top or 0)

    def _calculate_dynamic_viewport(self, table_model, background_params, config):
        """
        V5.1改进：动态计算视口尺寸，使用统一的尺寸估算系统

        Args:
            table_model: 表格模型
            background_params: 背景参数
            config: 渲染配置（用于精确尺寸估算）

        Returns:
            (viewport_width, viewport_height): 计算出的视口尺寸
        """
        # V5.1改进：使用resolver的精确估算方法，确保尺寸一致性
        if config is not None:
            estimated_width, estimated_height = self._get_unified_table_size_estimation(config)
            self.logger.debug(f"[UNIFIED_ESTIMATION] 使用统一尺寸估算: {estimated_width}x{estimated_height}")
        else:
            # 回退到原有的估算方法（向后兼容）
            estimated_width, estimated_height = self._estimate_table_render_size(table_model)
            self.logger.warning(f"[FALLBACK_ESTIMATION] config未提供，使用回退估算: {estimated_width}x{estimated_height}")

        # 基础尺寸：表格估算尺寸 + 安全边距
        base_width = estimated_width + 200  # 左右各100px边距
        base_height = estimated_height + 200  # 上下各100px边距

        # 考虑背景图模式的额外需求
        if background_params:
            # CSS模式：确保视口至少能容纳表格+背景边距
            min_width = max(base_width, background_params.css_crop_width or 1000)
            min_height = max(base_height, background_params.css_crop_height or 600)

            self.logger.debug(f"[DYNAMIC_VIEWPORT] CSS模式 - 表格尺寸: {estimated_width}x{estimated_height}")
            self.logger.debug(f"[DYNAMIC_VIEWPORT] CSS模式 - 基础尺寸: {base_width}x{base_height}")
            self.logger.debug(f"[DYNAMIC_VIEWPORT] CSS模式 - 背景要求: {background_params.css_crop_width}x{background_params.css_crop_height}")
        else:
            # 无背景模式：直接使用表格尺寸，但保证最小尺寸
            min_width = max(base_width, 1920)  # 保证最小宽度
            min_height = max(base_height, 1080)  # 保证最小高度

            self.logger.debug(f"[DYNAMIC_VIEWPORT] 无背景模式 - 表格尺寸: {estimated_width}x{estimated_height}")
            self.logger.debug(f"[DYNAMIC_VIEWPORT] 无背景模式 - 基础尺寸: {base_width}x{base_height}")

        # V5.3动态限制：基于实际需求设置最大限制，避免硬编码截断问题
        # 设置基础安全限制（避免内存问题）
        BASE_MAX_WIDTH = 8000   # 基础最大宽度
        BASE_MAX_HEIGHT = 6000  # 基础最大高度

        # 动态调整限制：如果实际需求超过基础限制，适当提高上限
        dynamic_max_width = max(BASE_MAX_WIDTH, min_width + 1000)  # 至少比需求大1000px
        dynamic_max_height = max(BASE_MAX_HEIGHT, min_height + 1000)  # 至少比需求大1000px

        # 设置绝对上限（防止内存溢出）
        ABSOLUTE_MAX_WIDTH = 20000   # 绝对最大宽度
        ABSOLUTE_MAX_HEIGHT = 20000  # 绝对最大高度

        final_max_width = min(dynamic_max_width, ABSOLUTE_MAX_WIDTH)
        final_max_height = min(dynamic_max_height, ABSOLUTE_MAX_HEIGHT)

        final_width = min(min_width, final_max_width)
        final_height = min(min_height, final_max_height)

        # 记录大表格处理信息
        if estimated_width > 2000 or estimated_height > 1500:
            self.logger.info(f"[LARGE_TABLE_VIEWPORT] 检测到大表格渲染需求")
            self.logger.info(f"[LARGE_TABLE_VIEWPORT] 表格估算尺寸: {estimated_width}x{estimated_height}")
            self.logger.info(f"[LARGE_TABLE_VIEWPORT] 需求尺寸: {min_width}x{min_height}")
            self.logger.info(f"[LARGE_TABLE_VIEWPORT] 动态限制: {final_max_width}x{final_max_height}")
            self.logger.info(f"[LARGE_TABLE_VIEWPORT] 最终视口尺寸: {final_width}x{final_height}")

            if final_width >= final_max_width or final_height >= final_max_height:
                self.logger.warning(f"[LARGE_TABLE_VIEWPORT] 视口尺寸达到动态限制: {final_max_width}x{final_max_height}")
                if final_max_width >= ABSOLUTE_MAX_WIDTH or final_max_height >= ABSOLUTE_MAX_HEIGHT:
                    self.logger.warning(f"[LARGE_TABLE_VIEWPORT] 已达到绝对上限: {ABSOLUTE_MAX_WIDTH}x{ABSOLUTE_MAX_HEIGHT}")
                    self.logger.warning(f"[LARGE_TABLE_VIEWPORT] 如果渲染失败，请考虑减少表格行列数")

        return final_width, final_height

    def _get_unified_table_size_estimation(self, config):
        """
        V5.1新增：使用resolver的统一尺寸估算系统

        Args:
            config: 渲染配置

        Returns:
            (estimated_width, estimated_height): 统一估算的表格尺寸
        """
        # 导入resolver模块以使用其精确估算方法
        from table_render.resolver import Resolver

        # 创建临时resolver实例进行尺寸估算
        temp_resolver = Resolver()

        # 使用resolver的精确估算方法
        estimated_width = temp_resolver._estimate_table_width(config)
        estimated_height = temp_resolver._estimate_table_height(config)

        self.logger.debug(f"[UNIFIED_SIZE_ESTIMATION] 使用resolver精确估算")
        self.logger.debug(f"[UNIFIED_SIZE_ESTIMATION] 表格尺寸: {estimated_width}x{estimated_height}")

        return estimated_width, estimated_height

    def _estimate_table_render_size(self, table_model):
        """
        V5.1已弃用：估算表格在浏览器中的实际渲染尺寸

        注意：此方法已被_get_unified_table_size_estimation替代，
        保留仅为向后兼容性，建议使用统一的尺寸估算系统。

        Args:
            table_model: 表格模型

        Returns:
            (estimated_width, estimated_height): 估算的渲染尺寸
        """
        self.logger.warning("[DEPRECATED] _estimate_table_render_size方法已弃用，建议使用统一尺寸估算系统")
        # 计算表格的行列数
        total_rows = len(table_model.header_rows) + len(table_model.body_rows)
        total_cols = 0

        # 计算最大列数
        for row in table_model.header_rows + table_model.body_rows:
            total_cols = max(total_cols, len(row.cells))

        # 基于经验值估算单元格尺寸
        # 这些值基于常见的表格渲染结果
        avg_cell_width = 120   # 平均单元格宽度
        avg_cell_height = 40   # 平均单元格高度
        border_width = 2       # 边框宽度

        # 考虑内容长度对宽度的影响
        max_content_length = 0
        for row in table_model.header_rows + table_model.body_rows:
            for cell in row.cells:
                if cell.content:
                    max_content_length = max(max_content_length, len(str(cell.content)))

        # 根据内容长度调整列宽
        if max_content_length > 20:
            avg_cell_width = min(200, avg_cell_width + (max_content_length - 20) * 3)

        # 计算总尺寸
        estimated_width = total_cols * avg_cell_width + (total_cols + 1) * border_width
        estimated_height = total_rows * avg_cell_height + (total_rows + 1) * border_width

        self.logger.debug(f"[SIZE_ESTIMATION] 表格结构: {total_rows}行 x {total_cols}列")
        self.logger.debug(f"[SIZE_ESTIMATION] 最大内容长度: {max_content_length}")
        self.logger.debug(f"[SIZE_ESTIMATION] 调整后单元格尺寸: {avg_cell_width}x{avg_cell_height}")
        self.logger.debug(f"[SIZE_ESTIMATION] 估算表格尺寸: {estimated_width}x{estimated_height}")

        return estimated_width, estimated_height

    @staticmethod
    async def create_async(debug_mode: bool = False, debug_output_dir: Optional[str] = None) -> 'HtmlRenderer':
        """
        异步工厂方法创建渲染器实例

        V5.2修复：正确管理Playwright实例生命周期，确保每个线程有完全独立的渲染环境。

        Args:
            debug_mode: 是否启用调试模式
            debug_output_dir: 调试输出目录

        Returns:
            初始化完成的渲染器实例
        """
        # V5.2修复：为每个渲染器创建独立的Playwright实例
        playwright = await async_playwright().start()

        # V5.2修复：使用独立的浏览器启动参数，避免进程间干扰
        browser = await playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--single-process',  # V5.2新增：强制单进程模式，避免进程间竞争
            ]
        )

        return HtmlRenderer(browser, playwright, debug_mode=debug_mode, debug_output_dir=debug_output_dir)

    async def close_async(self) -> None:
        """
        关闭渲染器并释放资源

        V5.2修复：正确关闭Playwright实例，避免资源泄漏和状态污染。
        """
        try:
            # 首先关闭浏览器
            if self.browser:
                await self.browser.close()
                self.logger.debug("浏览器实例已关闭")
        except Exception as e:
            self.logger.warning(f"关闭浏览器时发生错误: {e}")

        try:
            # V5.2修复：然后关闭Playwright实例
            if self.playwright_instance:
                await self.playwright_instance.stop()
                self.logger.debug("Playwright实例已关闭")
        except Exception as e:
            self.logger.warning(f"关闭Playwright实例时发生错误: {e}")
    
    async def render(self, table_model: TableModel, css_string: str = None, background_params=None, config=None) -> Tuple[bytes, Dict[str, Any]]:
        """
        渲染表格模型为图像并提取标注

        Args:
            table_model: 要渲染的表格模型
            css_string: 可选的CSS样式字符串
            background_params: 背景图参数（用于CSS渲染模式）
            config: 渲染配置（V5.1新增，用于统一尺寸估算）

        Returns:
            包含图像数据和标注的元组
        """
        self.logger.debug("开始渲染表格")

        with profile_stage("html_generation", {
            "css_length": len(css_string) if css_string else 0,
            "has_background": background_params is not None
        }):
            # 将TableModel转换为HTML（支持CSS背景图）
            html_content = self._table_model_to_html(table_model, css_string, background_params)

        with profile_stage("browser_page_creation"):
            # 创建新页面
            page = await self.browser.new_page()

        # 记录接收到的背景参数
        if background_params:
            self.logger.info(f"[HTML_DEBUG] 接收到背景参数:")
            self.logger.info(f"[HTML_DEBUG]   apply_background: {getattr(background_params, 'apply_background', 'None')}")
            self.logger.info(f"[HTML_DEBUG]   background_image_path: {getattr(background_params, 'background_image_path', 'None')}")
            self.logger.info(f"[HTML_DEBUG]   css_crop_width: {getattr(background_params, 'css_crop_width', 'None')}")
            self.logger.info(f"[HTML_DEBUG]   css_crop_height: {getattr(background_params, 'css_crop_height', 'None')}")
        else:
            self.logger.info(f"[HTML_DEBUG] 没有接收到背景参数 (background_params is None)")

        with profile_stage("viewport_calculation"):
            # V5.1改进：动态计算视口尺寸，支持超大表格
            # 传递配置信息以使用统一的尺寸估算系统
            viewport_width, viewport_height = self._calculate_dynamic_viewport(table_model, background_params, config)

        with profile_stage("viewport_setting"):
            await page.set_viewport_size({"width": viewport_width, "height": viewport_height})

        if background_params:
            self.logger.info(f"[HTML_DEBUG] CSS模式动态视口尺寸: {viewport_width}x{viewport_height}")
        else:
            self.logger.info(f"[HTML_DEBUG] 无背景模式动态视口尺寸: {viewport_width}x{viewport_height}")

        try:
            with profile_stage("page_content_setting", {
                "html_length": len(html_content)
            }):
                # 设置页面内容
                await page.set_content(html_content)

            # V5.2修复：增强CSS渲染稳定性，确保边框正确渲染
            with profile_stage("css_rendering_stabilization"):
                # 等待DOM完全构建
                await page.wait_for_load_state('domcontentloaded')

                # V5.2新增：验证关键CSS属性是否正确应用
                css_validation_passed = await self._validate_css_rendering(page)
                if not css_validation_passed:
                    self.logger.warning("CSS渲染验证失败，进行重试")
                    # 强制重新渲染CSS
                    await page.evaluate("document.body.style.display = 'none'; document.body.offsetHeight; document.body.style.display = 'block';")
                    await asyncio.sleep(0.5)

            # 如果使用了背景图，等待背景图加载完成
            if (background_params and
                background_params.background_image_path):
                with profile_stage("background_image_loading"):
                    self.logger.info(f"[LOAD_DEBUG] 等待背景图加载完成...")

                    # 等待页面完全加载，包括背景图
                    await page.wait_for_load_state('networkidle')

                    # V5.2修复：增加更长的等待时间确保背景图和CSS都完全加载
                    await asyncio.sleep(1.5)

                    # 验证背景图是否真的加载了（通过JavaScript）
                    bg_loaded = await page.evaluate("""
                    () => {
                        const body = document.body;
                        const computedStyle = window.getComputedStyle(body);
                        const bgImage = computedStyle.backgroundImage;
                        return bgImage && bgImage !== 'none' && !bgImage.includes('none');
                    }
                    """)

                    if bg_loaded:
                        self.logger.info(f"[LOAD_DEBUG] ✓ 背景图CSS已应用")
                    else:
                        self.logger.warning(f"[LOAD_DEBUG] ⚠ 背景图CSS未应用或为none")

                    self.logger.info(f"[LOAD_DEBUG] 背景图加载等待完成")
            else:
                with profile_stage("page_loading"):
                    # V5.2修复：等待网络空闲状态，确保所有资源加载完成
                    await page.wait_for_load_state('networkidle')
                    # V5.2新增：额外等待确保CSS完全渲染
                    await asyncio.sleep(1.0)

            with profile_stage("table_element_location"):
                # 定位表格元素（用于标注提取）
                table_element = await page.query_selector("#main-table")
                if not table_element:
                    raise RuntimeError("无法在页面中找到ID为 'main-table' 的表格元素")

            with profile_stage("annotation_extraction"):
                # 获取标注数据
                annotations = await page.evaluate(self._get_annotations_script())
                self.logger.debug(f"HTML渲染获取的原始标注: {annotations}")

            # 根据是否有背景参数选择不同的截图策略
            if background_params:
                with profile_stage("page_screenshot_with_background", {
                    "viewport_size": f"{viewport_width}x{viewport_height}"
                }):
                    # V5.2修复：截图前最后一次验证CSS状态
                    await self._ensure_css_stability(page)

                    # CSS渲染模式：截取整个页面（包含背景图）
                    image_bytes = await page.screenshot(type='png')
                    self.logger.debug("CSS模式：截取整个页面")

                # 调试：保存CSS模式原始截图和标注
                self._save_debug_stage("css_raw_screenshot", image_bytes, annotations, {
                    "render_mode": "css",
                    "viewport_size": f"{viewport_width}x{viewport_height}",
                    "background_image_path": getattr(background_params, 'background_image_path', None),
                    "css_table_left": getattr(background_params, 'css_table_left', None),
                    "css_table_top": getattr(background_params, 'css_table_top', None),
                    "css_background_width": getattr(background_params, 'css_background_width', None),
                    "css_background_height": getattr(background_params, 'css_background_height', None),
                    "css_crop_width": getattr(background_params, 'css_crop_width', None),
                    "css_crop_height": getattr(background_params, 'css_crop_height', None),
                    "css_bg_offset_x": getattr(background_params, 'css_bg_offset_x', None),
                    "css_bg_offset_y": getattr(background_params, 'css_bg_offset_y', None),
                    "table_bbox_from_js": annotations.get('table_bbox', None) if annotations else None
                })

                with profile_stage("annotation_coordinate_adjustment"):
                    # CSS模式下需要调整标注坐标，加上表格在页面中的偏移
                    original_annotations = annotations.copy() if annotations else None
                    annotations = self._adjust_annotations_for_css_mode(annotations, background_params)
                    self.logger.debug(f"CSS模式调整后的标注: {annotations}")

                # V4.4修改：移除CSS透视变换，统一使用OpenCV透视变换
                # 透视变换将在后处理阶段通过OpenCV实现，确保坐标变换的精确性
                self.logger.debug("CSS模式：跳过CSS透视变换，将在后处理阶段使用OpenCV实现")

                # 调试：保存CSS模式坐标调整后的结果
                # 获取实际使用的偏移值（从调整方法中获取）
                actual_offset_x, actual_offset_y = self._get_actual_table_offset(original_annotations, background_params)

                self._save_debug_stage("css_adjusted_annotations", image_bytes, annotations, {
                    "render_mode": "css",
                    "original_annotations": original_annotations,
                    "table_offset_x_used": actual_offset_x,
                    "table_offset_y_used": actual_offset_y,
                    "css_table_left": getattr(background_params, 'css_table_left', None),
                    "css_table_top": getattr(background_params, 'css_table_top', None),
                    "js_table_left": original_annotations.get('table_bbox', [None])[0] if original_annotations and 'table_bbox' in original_annotations else None,
                    "js_table_top": original_annotations.get('table_bbox', [None, None])[1] if original_annotations and 'table_bbox' in original_annotations else None,
                    "offset_source": "javascript" if original_annotations and 'table_bbox' in original_annotations else "css_fallback",
                    "adjustment_applied": True,
                    "coordinate_changes": self._calculate_coordinate_changes(original_annotations, annotations) if original_annotations and annotations else None
                })
            else:
                with profile_stage("table_element_screenshot"):
                    # 无背景模式：只截取表格元素
                    image_bytes = await table_element.screenshot(type='png')
                    self.logger.debug("无背景模式：截取表格元素")

                # 调试：保存无背景模式的表格截图
                self._save_debug_stage("no_background_table_screenshot", image_bytes, annotations, {
                    "render_mode": "no_background",
                    "screenshot_target": "table_element_only"
                })

            self.logger.debug(f"表格渲染完成，图像大小: {len(image_bytes)} bytes")
            self.logger.debug(f"HTML渲染返回的标注: {annotations}")
            return image_bytes, annotations
            
        finally:
            # 清理HTTP服务器
            self._stop_http_server()
            await page.close()

    def _adjust_annotations_for_css_mode(self, annotations, background_params):
        """
        CSS渲染模式下调整标注坐标

        在CSS模式下，页面截图包含整个页面，但标注坐标是相对于表格的。
        需要加上表格在页面中的偏移量，使标注坐标相对于整个页面。

        V4.1修复：使用JavaScript获取的实际表格位置，而不是CSS设置的理论位置。
        这样可以正确处理透视变换等CSS变换对表格位置的影响。

        Args:
            annotations: 原始标注数据（相对于表格）
            background_params: 背景参数，包含表格在页面中的位置

        Returns:
            调整后的标注数据（相对于整个页面）
        """
        if not annotations or 'cells' not in annotations:
            return annotations

        # V4.1修复：优先使用JavaScript获取的实际表格位置
        # 这样可以正确处理透视变换等CSS变换的影响
        if 'table_bbox' in annotations and annotations['table_bbox']:
            # 使用JavaScript获取的实际表格位置（包含所有CSS变换的影响）
            table_bbox = annotations['table_bbox']
            table_offset_x = table_bbox[0]  # 实际的left位置
            table_offset_y = table_bbox[1]  # 实际的top位置

            self.logger.info(f"[CSS_COORD_FIX] 使用JavaScript获取的实际表格位置: ({table_offset_x}, {table_offset_y})")

            # 记录CSS设置值用于对比
            css_table_left = background_params.css_table_left or 0
            css_table_top = background_params.css_table_top or 0

            if abs(table_offset_x - css_table_left) > 1 or abs(table_offset_y - css_table_top) > 1:
                self.logger.info(f"[CSS_COORD_FIX] 检测到CSS变换影响:")
                self.logger.info(f"[CSS_COORD_FIX]   CSS设置位置: ({css_table_left}, {css_table_top})")
                self.logger.info(f"[CSS_COORD_FIX]   实际视觉位置: ({table_offset_x}, {table_offset_y})")
                self.logger.info(f"[CSS_COORD_FIX]   变换偏移: ({table_offset_x - css_table_left}, {table_offset_y - css_table_top})")
        else:
            # 回退到CSS设置的位置（兼容性处理）
            table_offset_x = background_params.css_table_left or 0
            table_offset_y = background_params.css_table_top or 0

            self.logger.warning(f"[CSS_COORD_FIX] 未找到JavaScript表格位置，回退到CSS设置: ({table_offset_x}, {table_offset_y})")

        self.logger.debug(f"CSS模式标注调整，表格偏移: ({table_offset_x}, {table_offset_y})")

        # 调整每个单元格的坐标
        adjusted_annotations = {
            'cells': []
        }

        # 保持原始标注中的其他字段
        for key, value in annotations.items():
            if key != 'cells':
                adjusted_annotations[key] = value

        for i, cell in enumerate(annotations['cells']):
            if 'bbox' not in cell:
                self.logger.warning(f"单元格{i}缺少bbox字段，跳过调整")
                adjusted_annotations['cells'].append(cell)
                continue

            # 获取原始坐标（相对于表格）
            original_bbox = cell['bbox']
            self.logger.debug(f"单元格{i}原始坐标: {original_bbox}")

            # 调整坐标（加上表格偏移）
            adjusted_bbox = [
                original_bbox[0] + table_offset_x,  # x_min
                original_bbox[1] + table_offset_y,  # y_min
                original_bbox[2] + table_offset_x,  # x_max
                original_bbox[3] + table_offset_y   # y_max
            ]

            self.logger.debug(f"单元格{i}调整后坐标: {adjusted_bbox}")

            # 创建调整后的单元格数据
            adjusted_cell = cell.copy()
            adjusted_cell['bbox'] = adjusted_bbox
            adjusted_annotations['cells'].append(adjusted_cell)

        self.logger.debug(f"CSS模式标注调整完成，处理了{len(adjusted_annotations['cells'])}个单元格")
        return adjusted_annotations
    
    def _table_model_to_html(self, model: TableModel, css_string: str = None, background_params=None) -> str:
        """
        将TableModel转换为HTML字符串

        Args:
            model: 表格模型
            css_string: 可选的CSS样式字符串
            background_params: 背景图参数

        Returns:
            HTML字符串
        """
        # 生成CSS样式（包含背景图支持）
        css = self._generate_css_with_background(css_string, background_params)

        # 构建HTML
        html = f"<html><head>{css}</head><body>"
        html += "<table id='main-table'>"

        # V3.1新逻辑：分别渲染表头和主体
        # 渲染表头
        if model.header_rows:
            html += "<thead>"
            for row_model in model.header_rows:
                # 为行添加CSS类（包括尺寸类和边框类）
                row_classes = [
                    f"row-{row_model.row_index}",
                    "default-row",  # V3.4：默认行样式，如果有特定行配置会被覆盖
                ]
                html += f'<tr class="{" ".join(row_classes)}">'

                for cell_model in row_model.cells:
                    tag = "th"  # 表头始终使用th标签

                    # 构建CSS类列表（包括尺寸类）
                    cell_classes = [
                        f"col-{cell_model.col_index}",
                        f"cell-{cell_model.row_index}-{cell_model.col_index}",
                        "default-col",  # V3.4：默认列样式，如果有特定列配置会被覆盖
                    ]

                    html += f'<{tag} id="{cell_model.cell_id}" class="{" ".join(cell_classes)}"'

                    # 添加rowspan和colspan属性
                    if cell_model.row_span > 1:
                        html += f' rowspan="{cell_model.row_span}"'
                    if cell_model.col_span > 1:
                        html += f' colspan="{cell_model.col_span}"'

                    html += f'>{cell_model.content}</{tag}>'
                html += "</tr>"
            html += "</thead>"

        # 渲染主体
        if model.body_rows:
            html += "<tbody>"
            for row_model in model.body_rows:
                # 为行添加CSS类（包括尺寸类）
                row_classes = [
                    f"row-{row_model.row_index}",
                    "default-row",  # V3.4：默认行样式，如果有特定行配置会被覆盖
                ]
                html += f'<tr class="{" ".join(row_classes)}">'

                for cell_model in row_model.cells:
                    tag = "td"  # 主体始终使用td标签

                    # 构建CSS类列表（包括尺寸类）
                    cell_classes = [
                        f"col-{cell_model.col_index}",
                        f"cell-{cell_model.row_index}-{cell_model.col_index}",
                        "default-col",  # V3.4：默认列样式，如果有特定列配置会被覆盖
                    ]

                    html += f'<{tag} id="{cell_model.cell_id}" class="{" ".join(cell_classes)}"'

                    # 添加rowspan和colspan属性
                    if cell_model.row_span > 1:
                        html += f' rowspan="{cell_model.row_span}"'
                    if cell_model.col_span > 1:
                        html += f' colspan="{cell_model.col_span}"'

                    html += f'>{cell_model.content}</{tag}>'
                html += "</tr>"
            html += "</tbody>"
        
        html += "</table></body></html>"
        return html
    
    def _get_annotations_script(self) -> str:
        """
        V4.0优化：生成用于提取标注的JavaScript代码，减少DOM查询次数

        Returns:
            JavaScript代码字符串
        """
        return """
        () => {
            const annotations = {
                'cells': [],
                'table_bbox': null
            };

            const table = document.getElementById('main-table');
            if (!table) return annotations;

            // 一次性获取表格边界框
            const tableRect = table.getBoundingClientRect();
            annotations.table_bbox = [
                tableRect.left,
                tableRect.top,
                tableRect.right,
                tableRect.bottom
            ];

            // V4.0性能优化：批量获取所有单元格信息，减少重复的DOM查询
            const cells = table.querySelectorAll('th, td');
            const cellData = [];

            // 使用DocumentFragment或批量操作减少重排
            for (let i = 0; i < cells.length; i++) {
                const cell = cells[i];
                const rect = cell.getBoundingClientRect();

                cellData.push({
                    'id': cell.id,
                    'bbox': [
                        rect.left - tableRect.left,  // x_min
                        rect.top - tableRect.top,    // y_min
                        rect.right - tableRect.left, // x_max
                        rect.bottom - tableRect.top  // y_max
                    ],
                    'content': cell.textContent.trim(),
                    'tag_name': cell.tagName.toLowerCase(),
                    'row_span': parseInt(cell.getAttribute('rowspan') || '1'),
                    'col_span': parseInt(cell.getAttribute('colspan') || '1')
                });
            }

            annotations.cells = cellData;
            return annotations;
        }
        """

    def _generate_css_with_background(self, css_string: str = None, background_params=None) -> str:
        """
        生成包含背景图支持的CSS样式

        Args:
            css_string: 基础CSS样式字符串
            background_params: 背景图参数

        Returns:
            完整的CSS样式字符串
        """
        # 基础CSS样式
        if css_string:
            base_css = css_string
        else:
            # 默认CSS样式（向后兼容）
            base_css = """
                table {
                    border-collapse: collapse;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                }
                th, td {
                    border: 1px solid black;
                    padding: 8px;
                    text-align: left;
                    vertical-align: middle;
                }
                th {
                    background-color: #f0f0f0;
                    font-weight: bold;
                }
            """

        # 如果有背景图参数，添加CSS背景图支持
        if background_params:
            self.logger.info(f"[CSS_GEN_DEBUG] 生成CSS背景图样式")
            background_css = self._generate_background_css(background_params)
            table_transform_css = self._generate_table_transform_css(background_params)
            self.logger.info(f"[CSS_GEN_DEBUG] CSS背景图样式生成完成")

            full_css = f"""
            <style>
                {base_css}

                /* CSS背景图样式 */
                {background_css}

                /* 全局渲染优化 */
                * {{
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    text-rendering: optimizeLegibility;
                }}

                /* 表格和单元格渲染优化 */
                table, th, td {{
                    -webkit-backface-visibility: hidden;
                    backface-visibility: hidden;
                }}

                /* 表格位置和变换 */
                #main-table {{
                    {table_transform_css}
                }}
            </style>
            """
        else:
            full_css = f"<style>{base_css}</style>"

        return full_css

    def _generate_background_css(self, background_params) -> str:
        """生成背景图CSS样式"""
        bg_path = background_params.background_image_path or ''
        bg_width = background_params.css_background_width or 1200
        bg_height = background_params.css_background_height or 800
        crop_width = background_params.css_crop_width or 1000
        crop_height = background_params.css_crop_height or 600

        self.logger.info(f"生成背景CSS，路径: {bg_path}")
        self.logger.debug(f"背景参数: 尺寸{bg_width}x{bg_height}, 裁剪{crop_width}x{crop_height}")

        # 计算背景图位置（实现随机裁剪效果）
        # 使用Resolver计算的随机偏移实现随机裁剪
        bg_offset_x = background_params.css_bg_offset_x or 0
        bg_offset_y = background_params.css_bg_offset_y or 0

        # CSS background-position使用负值来实现裁剪效果
        bg_position_x = -bg_offset_x
        bg_position_y = -bg_offset_y

        # 计算可见区域和覆盖率
        visible_left = max(0, bg_position_x)
        visible_top = max(0, bg_position_y)
        visible_right = min(crop_width, bg_position_x + bg_width)
        visible_bottom = min(crop_height, bg_position_y + bg_height)
        visible_width = max(0, visible_right - visible_left)
        visible_height = max(0, visible_bottom - visible_top)
        coverage_ratio = (visible_width * visible_height) / (crop_width * crop_height)

        self.logger.info(f"[OFFSET_DEBUG] 背景图覆盖率: {coverage_ratio:.1%} ({visible_width}x{visible_height}/{crop_width}x{crop_height})")

        # 如果覆盖率太低，使用更安全的偏移
        if coverage_ratio < 0.5:  # 如果背景图覆盖率低于50%
            self.logger.warning(f"[OFFSET_DEBUG] 覆盖率过低，调整为安全偏移")
            # 使用更保守的偏移，确保至少75%的容器被背景图覆盖
            safe_offset_x = min(bg_offset_x, max(0, bg_width - int(crop_width * 0.75)))
            safe_offset_y = min(bg_offset_y, max(0, bg_height - int(crop_height * 0.75)))
            bg_position_x = -safe_offset_x
            bg_position_y = -safe_offset_y
            self.logger.info(f"[OFFSET_DEBUG] 调整后位置: ({bg_position_x}, {bg_position_y})")

        # 如果覆盖率极低，使用居中模式
        if coverage_ratio < 0.3:
            bg_position_x = -(bg_width - crop_width) // 2 if bg_width > crop_width else 0
            bg_position_y = -(bg_height - crop_height) // 2 if bg_height > crop_height else 0
            self.logger.warning(f"[OFFSET_DEBUG] 使用居中模式: ({bg_position_x}, {bg_position_y})")

        # 处理背景图路径，确保正确的URL格式
        import os
        self.logger.info(f"[BG_PATH_DEBUG] 开始检查背景图路径: '{bg_path}'")
        self.logger.info(f"[BG_PATH_DEBUG] 路径类型: {type(bg_path)}")
        self.logger.info(f"[BG_PATH_DEBUG] 路径长度: {len(bg_path) if bg_path else 0}")

        if bg_path:
            self.logger.info(f"[BG_PATH_DEBUG] os.path.exists('{bg_path}'): {os.path.exists(bg_path)}")
            if os.path.exists(bg_path):
                self.logger.info(f"[BG_PATH_DEBUG] os.path.isfile('{bg_path}'): {os.path.isfile(bg_path)}")
                self.logger.info(f"[BG_PATH_DEBUG] os.path.isdir('{bg_path}'): {os.path.isdir(bg_path)}")

        if bg_path and os.path.exists(bg_path) and os.path.isfile(bg_path):
            # 使用HTTP服务器提供背景图，避免file://协议的安全限制
            http_url = self._start_http_server(bg_path)
            if http_url:
                bg_url = http_url
                self.logger.info(f"[BG_PATH_DEBUG] 使用HTTP服务器提供背景图: {bg_url}")
            else:
                # HTTP服务器启动失败，回退到file://协议
                bg_url = f"file://{os.path.abspath(bg_path)}"
                self.logger.warning(f"[BG_PATH_DEBUG] HTTP服务器启动失败，使用file://协议: {bg_url}")
        else:
            # 如果背景图不存在，使用纯色背景作为fallback
            bg_url = "none"
            self.logger.error(f"[BG_PATH_DEBUG] 背景图路径无效，使用fallback")
            self.logger.error(f"[BG_PATH_DEBUG] 详细信息: bg_path='{bg_path}', exists={os.path.exists(bg_path) if bg_path else False}")

        # 生成最终的CSS
        if bg_url == "none":
            background_image_css = "background-image: none;"
        else:
            background_image_css = f"background-image: url('{bg_url}');"

        final_css = f"""
        body {{
            margin: 0;
            padding: 0;
            width: {crop_width}px;
            height: {crop_height}px;
            {background_image_css}
            background-size: {bg_width}px {bg_height}px;
            background-position: {bg_position_x}px {bg_position_y}px;
            background-repeat: no-repeat;
            background-color: #f0f0f0;
            overflow: hidden;
        }}
        """

        self.logger.info(f"[CSS_FINAL_DEBUG] 背景CSS生成完成")
        self.logger.info(f"[CSS_FINAL_DEBUG] 背景图URL: {bg_url}")
        self.logger.info(f"[CSS_FINAL_DEBUG] 背景尺寸: {bg_width}x{bg_height}, 位置: ({bg_position_x}, {bg_position_y})")

        return final_css

    def _generate_table_transform_css(self, background_params) -> str:
        """生成表格位置和变换CSS"""
        # 详细记录参数获取过程
        css_table_left_raw = getattr(background_params, 'css_table_left', None)
        css_table_top_raw = getattr(background_params, 'css_table_top', None)

        table_left = css_table_left_raw or 100
        table_top = css_table_top_raw or 100

        self.logger.info(f"[CSS_TABLE_POS] 表格位置参数:")
        self.logger.info(f"[CSS_TABLE_POS]   css_table_left (原始): {css_table_left_raw}")
        self.logger.info(f"[CSS_TABLE_POS]   css_table_top (原始): {css_table_top_raw}")
        self.logger.info(f"[CSS_TABLE_POS]   table_left (使用): {table_left}")
        self.logger.info(f"[CSS_TABLE_POS]   table_top (使用): {table_top}")

        if css_table_left_raw is None:
            self.logger.warning(f"[CSS_TABLE_POS] ⚠️ css_table_left 为 None，使用默认值 100")
        if css_table_top_raw is None:
            self.logger.warning(f"[CSS_TABLE_POS] ⚠️ css_table_top 为 None，使用默认值 100")

        # 基础位置样式
        position_css = f"""
        position: absolute;
        left: {table_left}px;
        top: {table_top}px;
        """

        # V4.4：移除表格级别的透视变换，改为全页面透视变换
        return position_css

    def _start_http_server(self, image_path: str) -> str:
        """
        启动临时HTTP服务器来提供背景图

        Args:
            image_path: 背景图文件路径

        Returns:
            HTTP URL
        """
        try:
            # 获取图片目录和文件名
            image_dir = os.path.dirname(image_path)
            image_filename = os.path.basename(image_path)

            # 找一个可用的端口
            import socket
            sock = socket.socket()
            sock.bind(('', 0))
            self.server_port = sock.getsockname()[1]
            sock.close()

            # 保存当前工作目录
            original_cwd = os.getcwd()

            # 创建HTTP服务器
            class QuietHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, **kwargs):
                    # 设置服务器的根目录为图片目录
                    super().__init__(*args, directory=image_dir, **kwargs)

                def log_message(self, format, *args):
                    pass  # 禁用日志输出

            self.http_server = socketserver.TCPServer(("", self.server_port), QuietHTTPRequestHandler)

            # 在后台线程中启动服务器
            self.http_thread = threading.Thread(target=self.http_server.serve_forever, daemon=True)
            self.http_thread.start()

            # 等待服务器启动
            time.sleep(0.2)  # 增加等待时间确保服务器完全启动

            http_url = f"http://localhost:{self.server_port}/{image_filename}"
            self.logger.info(f"[HTTP_SERVER_DEBUG] 启动HTTP服务器: {http_url}")
            self.logger.info(f"[HTTP_SERVER_DEBUG] 服务器根目录: {image_dir}")
            self.logger.info(f"[HTTP_SERVER_DEBUG] 请求文件名: {image_filename}")
            self.logger.info(f"[HTTP_SERVER_DEBUG] 完整文件路径: {image_path}")

            # 验证文件存在性
            if os.path.exists(image_path):
                file_size = os.path.getsize(image_path)
                self.logger.info(f"[HTTP_SERVER_DEBUG] ✓ 源文件存在 (大小: {file_size} bytes)")
            else:
                self.logger.error(f"[HTTP_SERVER_DEBUG] ✗ 源文件不存在")

            # 列出服务器目录中的文件（用于调试）
            try:
                files_in_dir = os.listdir(image_dir)
                self.logger.info(f"[HTTP_SERVER_DEBUG] 服务器目录中的文件数量: {len(files_in_dir)}")
                if image_filename in files_in_dir:
                    self.logger.info(f"[HTTP_SERVER_DEBUG] ✓ 目标文件在服务器目录中: {image_filename}")
                else:
                    self.logger.error(f"[HTTP_SERVER_DEBUG] ✗ 目标文件不在服务器目录中: {image_filename}")
                    self.logger.error(f"[HTTP_SERVER_DEBUG] 目录中的文件: {files_in_dir[:5]}...")
            except Exception as e:
                self.logger.error(f"[HTTP_SERVER_DEBUG] 无法列出服务器目录: {e}")

            return http_url

        except Exception as e:
            self.logger.error(f"[HTTP_SERVER_DEBUG] 启动HTTP服务器失败: {e}")
            return None

    def _stop_http_server(self):
        """停止HTTP服务器"""
        try:
            if self.http_server:
                self.http_server.shutdown()
                self.http_server.server_close()
                self.http_server = None
                self.logger.info(f"[HTTP_SERVER_DEBUG] HTTP服务器已停止")

            if self.http_thread:
                self.http_thread.join(timeout=1.0)
                self.http_thread = None

        except Exception as e:
            self.logger.error(f"[HTTP_SERVER_DEBUG] 停止HTTP服务器失败: {e}")

    # V4.5修改：移除CSS透视变换相关方法，统一使用OpenCV透视变换
    # 透视变换将在后处理阶段通过ImageAugmentor实现，确保坐标变换的精确性

    # V5.2新增：CSS渲染稳定性验证方法
    async def _validate_css_rendering(self, page) -> bool:
        """
        验证关键CSS属性是否正确应用

        Args:
            page: Playwright页面对象

        Returns:
            bool: CSS渲染是否正确
        """
        try:
            # 验证表格的border-collapse属性
            css_validation = await page.evaluate("""
            () => {
                const table = document.querySelector('#main-table');
                if (!table) return { success: false, error: 'table_not_found' };

                const computedStyle = window.getComputedStyle(table);
                const borderCollapse = computedStyle.borderCollapse;

                // 检查是否有单元格
                const cells = table.querySelectorAll('td, th');
                if (cells.length === 0) return { success: false, error: 'no_cells_found' };

                // 检查第一个单元格的边框样式
                const firstCell = cells[0];
                const cellStyle = window.getComputedStyle(firstCell);
                const borderWidth = cellStyle.borderWidth;
                const borderStyle = cellStyle.borderStyle;

                return {
                    success: true,
                    borderCollapse: borderCollapse,
                    cellCount: cells.length,
                    firstCellBorderWidth: borderWidth,
                    firstCellBorderStyle: borderStyle
                };
            }
            """)

            if not css_validation.get('success', False):
                self.logger.warning(f"CSS验证失败: {css_validation.get('error', 'unknown')}")
                return False

            # 记录CSS状态用于调试
            self.logger.debug(f"CSS验证通过: border-collapse={css_validation.get('borderCollapse')}, "
                            f"cells={css_validation.get('cellCount')}, "
                            f"border-width={css_validation.get('firstCellBorderWidth')}")

            return True

        except Exception as e:
            self.logger.warning(f"CSS验证过程中发生错误: {e}")
            return False

    async def _ensure_css_stability(self, page) -> None:
        """
        确保CSS渲染稳定性，特别是边框渲染

        Args:
            page: Playwright页面对象
        """
        try:
            # 强制重新计算样式
            await page.evaluate("""
            () => {
                // 强制重新计算所有样式
                const table = document.querySelector('#main-table');
                if (table) {
                    // 触发重排和重绘
                    table.style.visibility = 'hidden';
                    table.offsetHeight; // 强制重排
                    table.style.visibility = 'visible';

                    // 确保边框样式正确应用
                    const cells = table.querySelectorAll('td, th');
                    cells.forEach(cell => {
                        cell.offsetHeight; // 强制每个单元格重排
                    });
                }
            }
            """)

            # 短暂等待确保重排完成
            await asyncio.sleep(0.2)

        except Exception as e:
            self.logger.warning(f"CSS稳定性检查过程中发生错误: {e}")
