# -*- coding: utf-8 -*-
"""
全局metadata收集器
用于收集生成过程中的详细选择信息，便于调试和分析
"""

import threading
from typing import Any, Dict, Optional
import copy


class MetadataCollector:
    """
    全局metadata收集器
    
    线程安全的单例模式，用于收集生成过程中的各种选择信息
    """
    
    _instance = None
    _lock = threading.Lock()
    _local = threading.local()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
    
    def _get_thread_data(self) -> Dict[str, Any]:
        """获取当前线程的metadata数据"""
        if not hasattr(self._local, 'metadata'):
            self._local.metadata = {}
        return self._local.metadata
    
    def clear(self):
        """清空当前线程的metadata数据"""
        if hasattr(self._local, 'metadata'):
            self._local.metadata.clear()
    
    def record_file_selection(self, category: str, selected_file: str, selected_directory: str, 
                            directory_probability: float, all_directories: list = None, 
                            all_probabilities: list = None):
        """
        记录文件选择信息
        
        Args:
            category: 类别（如background, font, csv等）
            selected_file: 选择的具体文件路径
            selected_directory: 选择的目录路径
            directory_probability: 该目录对应的概率
            all_directories: 所有可选目录列表（可选）
            all_probabilities: 所有目录对应的概率列表（可选）
        """
        data = self._get_thread_data()
        
        # 记录选择的文件和目录信息
        data[f"selected_{category}_file"] = selected_file
        data[f"selected_{category}_directory"] = selected_directory
        data[f"{category}_directory_probability"] = directory_probability
        
        # 可选：记录所有选项（用于完整的选择上下文）
        if all_directories is not None:
            data[f"{category}_all_directories"] = all_directories
        if all_probabilities is not None:
            data[f"{category}_all_directory_probabilities"] = all_probabilities
    
    def record_range_selection(self, category: str, selected_value: Any, selected_range: list, 
                             range_probability: float, all_ranges: list = None, 
                             all_probabilities: list = None):
        """
        记录概率区间选择信息
        
        Args:
            category: 类别（如margin, font_size, body_rows等）
            selected_value: 选择的具体值
            selected_range: 选择的范围
            range_probability: 该范围对应的概率
            all_ranges: 所有可选范围列表（可选）
            all_probabilities: 所有范围对应的概率列表（可选）
        """
        data = self._get_thread_data()
        
        # 记录选择的值和范围信息
        data[f"selected_{category}"] = selected_value
        data[f"selected_{category}_range"] = selected_range
        data[f"{category}_range_probability"] = range_probability
        
        # 可选：记录所有选项（用于完整的选择上下文）
        if all_ranges is not None:
            data[f"{category}_all_ranges"] = all_ranges
        if all_probabilities is not None:
            data[f"{category}_all_range_probabilities"] = all_probabilities
    
    def record_option_selection(self, category: str, selected_option: Any, option_probability: float,
                               all_options: list = None, all_probabilities: list = None):
        """
        记录选项选择信息（用于非范围类的选择，如字体族、对齐方式等）
        
        Args:
            category: 类别（如font_family, horizontal_align等）
            selected_option: 选择的选项
            option_probability: 该选项对应的概率
            all_options: 所有可选选项列表（可选）
            all_probabilities: 所有选项对应的概率列表（可选）
        """
        data = self._get_thread_data()
        
        # 记录选择的选项信息
        data[f"selected_{category}"] = selected_option
        data[f"{category}_option_probability"] = option_probability
        
        # 可选：记录所有选项
        if all_options is not None:
            data[f"{category}_all_options"] = all_options
        if all_probabilities is not None:
            data[f"{category}_all_option_probabilities"] = all_probabilities
    
    def record_intermediate_value(self, key: str, value: Any):
        """
        记录中间计算值（用于debug）
        
        Args:
            key: 键名
            value: 值
        """
        data = self._get_thread_data()
        data[key] = value
    
    def record_multiple_values(self, values: Dict[str, Any]):
        """
        批量记录多个值
        
        Args:
            values: 键值对字典
        """
        data = self._get_thread_data()
        data.update(values)
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取当前线程的所有metadata"""
        return copy.deepcopy(self._get_thread_data())
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """获取特定键的值"""
        data = self._get_thread_data()
        return data.get(key, default)
    
    def has_key(self, key: str) -> bool:
        """检查是否存在特定键"""
        data = self._get_thread_data()
        return key in data


# 全局实例
metadata_collector = MetadataCollector()
