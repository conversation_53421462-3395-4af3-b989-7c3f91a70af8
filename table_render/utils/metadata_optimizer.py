# -*- coding: utf-8 -*-
"""
Metadata优化器
将原始的详细metadata转换为优化的结构化格式
"""

import logging
from typing import Dict, Any, Optional, List


class MetadataOptimizer:
    """
    Metadata优化器
    
    将包含大量冗余信息的原始metadata转换为结构化的优化格式
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def optimize_metadata(
        self, 
        resolved_params: Dict[str, Any],
        collected_metadata: Dict[str, Any],
        csv_sampling_info: Optional[Dict[str, Any]],
        sample_seed: int,
        sample_index: int,
        generation_timestamp: float,
        turbo_jpeg_used: bool
    ) -> Dict[str, Any]:
        """
        将原始metadata转换为优化格式
        
        Args:
            resolved_params: 解析后的参数
            collected_metadata: metadata_collector收集的信息
            csv_sampling_info: CSV采样信息
            sample_seed: 样本种子
            sample_index: 样本索引
            generation_timestamp: 生成时间戳
            turbo_jpeg_used: 是否使用TurboJPEG
            
        Returns:
            优化后的metadata字典
        """
        optimized = {
            "sample_seed": sample_seed,
            "sample_index": sample_index,
            "generation_timestamp": generation_timestamp,
            "turbo_jpeg_used": turbo_jpeg_used
        }
        
        # 1. 提取结构选择信息
        optimized["structure_choices"] = self._extract_structure_choices(
            resolved_params, collected_metadata
        )
        
        # 2. 提取样式选择信息
        optimized["style_choices"] = self._extract_style_choices(
            resolved_params, collected_metadata
        )
        
        # 3. 提取文件选择信息
        optimized["file_choices"] = self._extract_file_choices(collected_metadata)
        
        # 4. 提取后处理选择信息
        optimized["postprocessing_choices"] = self._extract_postprocessing_choices(
            resolved_params, collected_metadata
        )
        
        # 5. 提取性能信息
        optimized["performance_info"] = self._extract_performance_info(resolved_params)
        
        # 6. 提取继承应用信息
        optimized["inheritance_applied"] = self._extract_inheritance_info(collected_metadata)
        
        # 7. 提取CSS渲染信息
        optimized["css_render_info"] = self._extract_css_render_info(collected_metadata)
        
        # 8. 保留CSV采样信息
        if csv_sampling_info:
            optimized["csv_sampling_info"] = csv_sampling_info
        
        # 9. 提取关键解析参数
        optimized["resolved_key_params"] = self._extract_key_params(resolved_params)
        
        return optimized
    
    def _extract_structure_choices(
        self, 
        resolved_params: Dict[str, Any], 
        collected_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取表格结构选择信息"""
        structure_choices = {}
        
        # 表格结构参数
        structure_params = [
            "header_rows", "body_rows", "cols", "merge_probability",
            "max_row_span", "max_col_span"
        ]
        
        for param in structure_params:
            if f"selected_{param}" in collected_metadata:
                choice_info = {
                    "value": collected_metadata[f"selected_{param}"]
                }
                
                # 添加范围信息（如果存在）
                if f"selected_{param}_range" in collected_metadata:
                    choice_info["range"] = collected_metadata[f"selected_{param}_range"]
                
                # 添加概率信息
                if f"{param}_range_probability" in collected_metadata:
                    choice_info["probability"] = collected_metadata[f"{param}_range_probability"]
                elif f"{param}_option_probability" in collected_metadata:
                    choice_info["probability"] = collected_metadata[f"{param}_option_probability"]
                
                structure_choices[param] = choice_info
        
        return structure_choices
    
    def _extract_style_choices(
        self, 
        resolved_params: Dict[str, Any], 
        collected_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取样式选择信息"""
        style_choices = {}
        
        # 样式参数
        style_params = [
            "default_family", "default_size", "horizontal_align", 
            "vertical_align", "padding"
        ]
        
        for param in style_params:
            # 处理字体族的特殊命名
            if param == "default_family":
                metadata_key = "font_family"
                final_key = "font_family"
            elif param == "default_size":
                metadata_key = "font_size"
                final_key = "font_size"
            else:
                metadata_key = param
                final_key = param

            # 检查是否存在选择信息
            if f"selected_{metadata_key}" in collected_metadata:
                choice_info = {
                    "value": collected_metadata[f"selected_{metadata_key}"]
                }

                # 添加范围信息（如果存在）
                if f"selected_{metadata_key}_range" in collected_metadata:
                    choice_info["range"] = collected_metadata[f"selected_{metadata_key}_range"]

                # 添加概率信息
                if f"{metadata_key}_range_probability" in collected_metadata:
                    choice_info["probability"] = collected_metadata[f"{metadata_key}_range_probability"]
                elif f"{metadata_key}_option_probability" in collected_metadata:
                    choice_info["probability"] = collected_metadata[f"{metadata_key}_option_probability"]

                style_choices[final_key] = choice_info
        
        return style_choices
    
    def _extract_file_choices(self, collected_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """提取文件选择信息"""
        file_choices = {}

        file_categories = ["csv", "font", "background"]

        for category in file_categories:
            # 检查是否存在文件选择信息
            file_key = f"selected_{category}_file"
            dir_key = f"selected_{category}_directory"
            prob_key = f"{category}_directory_probability"

            if file_key in collected_metadata:
                file_info = {
                    "file": collected_metadata[file_key],
                    "directory": collected_metadata.get(dir_key, ""),
                    "probability": collected_metadata.get(prob_key, 0.0)
                }
                file_choices[category] = file_info
            elif dir_key in collected_metadata:
                # 即使没有具体文件，但有目录信息也记录
                file_info = {
                    "file": "",
                    "directory": collected_metadata[dir_key],
                    "probability": collected_metadata.get(prob_key, 0.0)
                }
                file_choices[category] = file_info

        return file_choices
    
    def _extract_postprocessing_choices(
        self, 
        resolved_params: Dict[str, Any], 
        collected_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取后处理选择信息"""
        postprocessing_choices = {}
        
        # Margin control信息
        if "selected_margin_control" in collected_metadata:
            postprocessing_choices["margin_control"] = {
                "value": collected_metadata["selected_margin_control"],
                "range": collected_metadata.get("selected_margin_control_range", []),
                "probability": collected_metadata.get("margin_control_range_probability", 0.0)
            }
        
        # 降质效果应用情况
        degradation_effects = []
        degradation_types = [
            "degradation_blur", "degradation_noise", "degradation_fade_global",
            "degradation_fade_local", "degradation_uneven_lighting", "degradation_jpeg",
            "degradation_darker_brighter", "degradation_gamma_correction"
        ]
        
        # 检查哪些降质效果被应用了（这里需要根据实际应用情况来判断）
        postprocessing_choices["degradation_effects_applied"] = degradation_effects
        
        # 透视变换应用情况
        postprocessing_choices["perspective_applied"] = collected_metadata.get("apply_perspective", False)
        
        # 背景图应用情况
        postprocessing_choices["background_applied"] = collected_metadata.get("apply_background", False)
        
        # 表格融合设置
        table_blending_enabled = False
        transparency_settings = {}

        if "postprocessing" in resolved_params and resolved_params["postprocessing"] is not None:
            postprocessing = resolved_params["postprocessing"]
            if "table_blending" in postprocessing and postprocessing["table_blending"] is not None:
                table_blending = postprocessing["table_blending"]
                table_blending_enabled = table_blending.get("enable_transparency", False)

                # 始终记录transparency_settings配置，不管是否启用
                transparency_settings = {
                    "default_color_transparency": table_blending.get("default_color_transparency", 0.0),
                    "meaningful_color_transparency": table_blending.get("meaningful_color_transparency", 0.7)
                }

        postprocessing_choices["table_blending_enabled"] = table_blending_enabled
        # 始终添加transparency_settings，即使为空也要保持结构一致性
        postprocessing_choices["transparency_settings"] = transparency_settings
        
        return postprocessing_choices
    
    def _extract_performance_info(self, resolved_params: Dict[str, Any]) -> Dict[str, Any]:
        """提取性能信息"""
        performance_info = {}

        if "performance" in resolved_params and resolved_params["performance"] is not None:
            perf = resolved_params["performance"]
            performance_info = {
                "parallel_enabled": perf.get("enable_parallel", False),
                "max_workers": perf.get("max_workers", 1),
                "turbo_jpeg_enabled": perf.get("enable_turbo_jpeg", False),
                "turbo_jpeg_quality": perf.get("turbo_jpeg_quality", 95),
                "turbo_jpeg_format": perf.get("turbo_jpeg_format", "png"),
                "css_stability_enabled": perf.get("css_stability_enabled", False)
            }

        return performance_info
    
    def _extract_inheritance_info(self, collected_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """提取继承应用信息"""
        inheritance_applied = {
            "font_family_changed": collected_metadata.get("inheritance_font_family_changed", False),
            "font_size_changed": collected_metadata.get("inheritance_font_size_changed", False),
            "alignment_changed": collected_metadata.get("inheritance_alignment_changed", False),
            "padding_changed": collected_metadata.get("inheritance_padding_changed", False),
            "text_color_changed": collected_metadata.get("inheritance_text_color_changed", False),
            "background_color_changed": collected_metadata.get("inheritance_background_color_changed", False)
        }

        return inheritance_applied
    
    def _extract_css_render_info(self, collected_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """提取CSS渲染关键信息"""
        css_render_info = {}
        
        # 表格位置
        if "css_table_left" in collected_metadata and "css_table_top" in collected_metadata:
            css_render_info["table_position"] = {
                "left": collected_metadata["css_table_left"],
                "top": collected_metadata["css_table_top"]
            }
        
        # 背景尺寸
        if "css_background_width" in collected_metadata and "css_background_height" in collected_metadata:
            css_render_info["background_dimensions"] = {
                "width": collected_metadata["css_background_width"],
                "height": collected_metadata["css_background_height"]
            }
        
        # 裁剪尺寸
        if "css_crop_width" in collected_metadata and "css_crop_height" in collected_metadata:
            css_render_info["crop_dimensions"] = {
                "width": collected_metadata["css_crop_width"],
                "height": collected_metadata["css_crop_height"]
            }
        
        # 背景偏移
        if "css_bg_offset_x" in collected_metadata and "css_bg_offset_y" in collected_metadata:
            css_render_info["background_offset"] = {
                "x": collected_metadata["css_bg_offset_x"],
                "y": collected_metadata["css_bg_offset_y"]
            }
        
        # 缩放因子
        scaling_factors = {}
        if "max_scale_factor" in collected_metadata:
            scaling_factors["max_scale_factor"] = collected_metadata["max_scale_factor"]
        if "content_area_shrink_ratio" in collected_metadata:
            scaling_factors["content_area_shrink_ratio"] = collected_metadata["content_area_shrink_ratio"]
        
        if scaling_factors:
            css_render_info["scaling_factors"] = scaling_factors
        
        return css_render_info
    
    def _extract_key_params(self, resolved_params: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键解析参数"""
        key_params = {}
        
        # 表格尺寸
        if "structure" in resolved_params:
            structure = resolved_params["structure"]
            key_params["table_size"] = {
                "header_rows": structure.get("header_rows", 1),
                "body_rows": structure.get("body_rows", 5),
                "cols": structure.get("cols", 3)
            }
        
        # 内容源
        if "content" in resolved_params:
            key_params["content_source"] = resolved_params["content"].get("source_type", "unknown")
        
        # 样式关键参数
        if "style" in resolved_params:
            style = resolved_params["style"]
            key_params["overflow_strategy"] = style.get("overflow_strategy", "wrap")
            
            if "border_mode" in style:
                border_mode = style["border_mode"]
                if isinstance(border_mode, dict):
                    key_params["border_mode"] = border_mode.get("mode", "full")
                else:
                    key_params["border_mode"] = str(border_mode)
            
            key_params["zebra_stripes_enabled"] = style.get("zebra_stripes", 0) > 0
        
        # 透明度设置
        transparency_enabled = False
        if "postprocessing" in resolved_params and "table_blending" in resolved_params["postprocessing"]:
            transparency_enabled = resolved_params["postprocessing"]["table_blending"].get("enable_transparency", False)
        key_params["transparency_enabled"] = transparency_enabled
        
        # 随机种子
        key_params["random_seed"] = resolved_params.get("seed", 0)
        
        return key_params


# 全局实例
metadata_optimizer = MetadataOptimizer()
