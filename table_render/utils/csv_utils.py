"""
CSV文件处理工具模块

提供CSV文件的扫描、选择和验证功能，支持概率化目录选择。
"""

import logging
import os
from pathlib import Path
from typing import List, Optional, Tuple
import random

from .prob_utils import choose_from_list, normalize_probabilities


class CSVFileSelector:
    """CSV文件选择器"""
    
    def __init__(self, random_state: random.Random = None):
        """
        初始化CSV文件选择器
        
        Args:
            random_state: 随机数生成器，用于确保可复现性
        """
        self.random_state = random_state or random.Random()
        self.logger = logging.getLogger(__name__)
    
    def scan_csv_files_in_dir(self, directory: str) -> List[str]:
        """
        扫描目录中的所有CSV文件
        
        Args:
            directory: 目录路径
            
        Returns:
            CSV文件路径列表
            
        Raises:
            FileNotFoundError: 如果目录不存在
        """
        dir_path = Path(directory)
        
        if not dir_path.exists():
            raise FileNotFoundError(f"目录不存在: {directory}")
        
        if not dir_path.is_dir():
            raise ValueError(f"路径不是目录: {directory}")
        
        # 扫描.csv文件
        csv_files = []
        try:
            for file_path in dir_path.glob("*.csv"):
                if file_path.is_file():
                    csv_files.append(str(file_path))
            
            self.logger.debug(f"在目录 {directory} 中找到 {len(csv_files)} 个CSV文件")
            return sorted(csv_files)  # 排序确保一致性
            
        except Exception as e:
            self.logger.error(f"扫描目录 {directory} 时出错: {e}")
            raise
    
    def select_csv_file_from_dirs(
        self, 
        csv_dirs: List[str], 
        csv_dir_probabilities: Optional[List[float]] = None
    ) -> str:
        """
        从多个目录中概率化选择一个CSV文件
        
        Args:
            csv_dirs: CSV目录列表
            csv_dir_probabilities: 目录选择概率，如果为None则等概率选择
            
        Returns:
            选中的CSV文件路径
            
        Raises:
            ValueError: 如果没有找到可用的CSV文件
            FileNotFoundError: 如果所有目录都不存在
        """
        if not csv_dirs:
            raise ValueError("csv_dirs不能为空")
        
        # 处理概率
        if csv_dir_probabilities is None:
            # 等概率选择
            probabilities = [1.0 / len(csv_dirs)] * len(csv_dirs)
        else:
            if len(csv_dir_probabilities) != len(csv_dirs):
                raise ValueError(f"概率列表长度({len(csv_dir_probabilities)})与目录列表长度({len(csv_dirs)})不匹配")
            probabilities = normalize_probabilities(csv_dir_probabilities)
        
        # 尝试从每个目录中找到CSV文件
        available_dirs = []
        available_probs = []
        
        for i, directory in enumerate(csv_dirs):
            try:
                csv_files = self.scan_csv_files_in_dir(directory)
                if csv_files:  # 只有包含CSV文件的目录才被考虑
                    available_dirs.append((directory, csv_files))
                    available_probs.append(probabilities[i])
                else:
                    self.logger.warning(f"目录 {directory} 中没有找到CSV文件")
            except FileNotFoundError:
                self.logger.warning(f"目录不存在: {directory}")
            except Exception as e:
                self.logger.error(f"处理目录 {directory} 时出错: {e}")
        
        if not available_dirs:
            raise ValueError(f"在所有指定目录中都没有找到可用的CSV文件: {csv_dirs}")
        
        # 重新归一化可用目录的概率
        if len(available_probs) < len(probabilities):
            available_probs = normalize_probabilities(available_probs)
        
        # 选择目录
        selected_dir, csv_files = choose_from_list(
            available_dirs, 
            available_probs, 
            self.random_state
        )
        
        # 从选中目录中随机选择一个CSV文件
        selected_file = self.random_state.choice(csv_files)
        
        self.logger.info(f"选择CSV文件: {selected_file} (来自目录: {selected_dir})")
        return selected_file
    
    def validate_csv_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证CSV文件是否符合要求
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            file_path_obj = Path(file_path)
            
            # 检查文件是否存在
            if not file_path_obj.exists():
                return False, f"文件不存在: {file_path}"
            
            if not file_path_obj.is_file():
                return False, f"路径不是文件: {file_path}"
            
            # 检查文件扩展名
            if file_path_obj.suffix.lower() != '.csv':
                return False, f"文件不是CSV格式: {file_path}"
            
            # 简单检查文件是否可读
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    # 读取前几行检查基本格式
                    lines = []
                    for i, line in enumerate(f):
                        lines.append(line.strip())
                        if i >= 2:  # 只检查前3行
                            break
                    
                    if len(lines) < 2:
                        return False, f"CSV文件行数不足(需要至少2行): {file_path}"
                    
                    # 检查是否有内容
                    if not any(line for line in lines):
                        return False, f"CSV文件为空: {file_path}"
                    
                    self.logger.debug(f"CSV文件验证通过: {file_path}")
                    return True, ""
                    
            except UnicodeDecodeError:
                return False, f"CSV文件编码错误: {file_path}"
            except Exception as e:
                return False, f"读取CSV文件时出错: {file_path}, 错误: {e}"
                
        except Exception as e:
            return False, f"验证CSV文件时出错: {file_path}, 错误: {e}"


def select_csv_file(
    file_path: Optional[str] = None,
    csv_dirs: Optional[List[str]] = None,
    csv_dir_probabilities: Optional[List[float]] = None,
    random_state: random.Random = None
) -> str:
    """
    选择CSV文件的便捷函数

    Args:
        file_path: 单个文件路径（优先级低于csv_dirs）
        csv_dirs: CSV目录列表
        csv_dir_probabilities: 目录选择概率
        random_state: 随机数生成器

    Returns:
        选中的CSV文件路径

    Raises:
        ValueError: 如果没有提供有效的配置或找不到文件
    """
    from .metadata_collector import metadata_collector

    selector = CSVFileSelector(random_state)

    # csv_dirs优先
    if csv_dirs:
        selected_file = selector.select_csv_file_from_dirs(csv_dirs, csv_dir_probabilities)

        # 记录CSV文件选择信息到metadata
        if csv_dir_probabilities and len(csv_dir_probabilities) == len(csv_dirs):
            # 找到选择的文件属于哪个目录
            selected_directory = None
            for csv_dir in csv_dirs:
                if selected_file.startswith(csv_dir):
                    selected_directory = csv_dir
                    break

            if selected_directory:
                try:
                    dir_index = csv_dirs.index(selected_directory)
                    selected_dir_probability = csv_dir_probabilities[dir_index]
                    metadata_collector.record_file_selection(
                        category="csv",
                        selected_file=selected_file,
                        selected_directory=selected_directory,
                        directory_probability=selected_dir_probability,
                        all_directories=csv_dirs,
                        all_probabilities=csv_dir_probabilities
                    )
                except (ValueError, IndexError):
                    pass  # 如果找不到对应的概率，跳过记录

        return selected_file

    # 回退到单文件模式
    if file_path:
        is_valid, error_msg = selector.validate_csv_file(file_path)
        if not is_valid:
            raise ValueError(f"CSV文件无效: {error_msg}")
        return file_path

    raise ValueError("必须提供csv_dirs或file_path中的至少一个")
