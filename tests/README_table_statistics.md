# TableRender 统计分析脚本

## 概述

`table_statistics.py` 是一个用于分析TableRender生成结果的统计脚本，可以对生成的表格图像、标注和metadata进行全面的统计分析，验证配置参数的实际效果。

## 功能特性

### 1. 表格结构统计
- **表格尺寸分布**: 按5为区间统计行数、列数分布
- **表头行数分布**: 1行、2行、3行表头的占比
- **合并单元格统计**: 合并单元格数量和跨度分布
- **合并率验证**: 实际合并率与配置概率的对比

### 2. 内容来源统计
- **CSV数据源分布**: nl2sql_train vs wikisql_train的选择比例
- **采样比例统计**: 行列采样比例的分布情况
- **空白控制效果**: 空白单元格的生成情况

### 3. 样式特征统计
- **字体分布**: 字体族、字体大小的选择分布
- **对齐方式统计**: 水平和垂直对齐的分布
- **样式继承统计**: 继承变化的实际应用率

### 4. 后处理效果统计
- **边距控制分布**: 按20px区间统计边距值
- **背景图使用**: 背景图目录选择和应用情况
- **降质效果统计**: 各种降质效果的应用率
- **透视变换统计**: 透视变换的应用情况

### 5. 性能相关统计
- **TurboJPEG使用率**: TurboJPEG优化的使用情况
- **并行处理统计**: 并行vs串行生成的分布
- **工作线程分布**: 不同线程数的使用情况

### 6. 文件相关统计
- **图像文件大小**: 按100KB区间统计文件大小
- **字体来源分布**: common vs rare字体目录的使用

## 使用方法

### 基本用法
```bash
python tests/table_statistics.py /path/to/output/dir
```

### 指定报告文件
```bash
python tests/table_statistics.py /path/to/output/dir --report-file my_stats.json
```

### 详细日志模式
```bash
python tests/table_statistics.py /path/to/output/dir --verbose
```

### 静默模式
```bash
python tests/table_statistics.py /path/to/output/dir --quiet
```

## 输入要求

输入目录必须包含以下三个子文件夹：
- `annotations/` - 包含标注JSON文件
- `images/` - 包含生成的表格图像
- `metadata/` - 包含metadata JSON文件

文件命名规则：
- 图像文件: `000001.png` 或 `000001.jpg`
- 标注文件: `000001.json`
- Metadata文件: `000001.json`

## 输出格式

### 控制台输出
脚本会在控制台显示统计摘要，包括：
- 总样本数和分析时间
- 主要统计信息的概览
- 各类分布的百分比

### JSON报告文件
生成详细的JSON格式统计报告，包含：

```json
{
  "summary": {
    "total_samples": 1000,
    "analysis_date": "2024-01-01 12:00:00"
  },
  "structure_statistics": {
    "table_size_distribution": {
      "body_rows_2-6": {"count": 200, "percentage": 20.0},
      "cols_2-6": {"count": 500, "percentage": 50.0}
    },
    "merge_statistics": {
      "merge_count_0-4": {"count": 800, "percentage": 80.0}
    }
  },
  "content_statistics": {
    "csv_source_distribution": {
      "nl2sql": {"count": 500, "percentage": 50.0},
      "wikisql": {"count": 500, "percentage": 50.0}
    }
  },
  "style_statistics": {
    "font_distribution": {
      "Arial": {"count": 400, "percentage": 40.0}
    },
    "inheritance_statistics": {
      "font_family_changed": {
        "applied": 200,
        "not_applied": 800,
        "application_rate": 20.0
      }
    }
  }
}
```

## 统计区间说明

### 数值区间
- **行数/列数**: 以5为区间 (2-6, 7-11, 12-16, ...)
- **合并单元格数**: 以5为区间 (0-4, 5-9, 10-14, ...)
- **字体大小**: 以2px为区间 (10-11px, 12-13px, ...)
- **边距值**: 以20px为区间 (30-49px, 50-69px, ...)
- **文件大小**: 以100KB为区间 (<100KB, 100-200KB, ...)

### 比例区间
- **采样比例**: 20%为区间 (0-20%, 20-40%, ...)
- **应用率**: 实际应用次数 / 总次数 * 100%

## 配置验证

脚本可以验证以下配置参数的实际效果：

### v5_complete.yaml中的概率分布
- `header_rows.probability_list: [0.6, 0.3, 0.1]`
- `body_rows.probability_list: [0.2, 0.25, 0.45, 0.09, 0.01]`
- `cols.probability_list: [0.5, 0.45, 0.05]`
- `csv_dir_probabilities: [0.5, 0.5]`
- `font_dir_probabilities: [0.8, 0.2]`
- `background_dir_probabilities: [0.5, 0.1, 0.1, 0.2, ...]`

### 样式继承概率
- `font_family_change_probability: 0.2`
- `font_size_change_probability: 0.2`
- `alignment_change_probability: 0.1`
- `padding_change_probability: 0.4`

## 注意事项

1. **文件完整性**: 确保所有样本的三个文件（图像、标注、metadata）都存在
2. **内存使用**: 大量样本可能占用较多内存，建议分批处理
3. **处理时间**: 统计分析的时间与样本数量成正比
4. **错误处理**: 单个样本的错误不会中断整个分析过程

## 故障排除

### 常见错误
1. **目录不存在**: 检查输入路径是否正确
2. **权限问题**: 确保有读取输入目录和写入报告文件的权限
3. **JSON格式错误**: 检查metadata或标注文件的JSON格式是否正确
4. **内存不足**: 减少样本数量或增加系统内存

### 调试建议
- 使用 `--verbose` 参数查看详细日志
- 检查第一个出错的样本文件
- 验证文件命名是否符合规范
