#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TableRender 统计分析脚本

基于生成的表格图像、标注和metadata进行统计分析，
验证配置参数的实际效果和生成结果的分布情况。

使用方法:
    python tests/table_statistics.py /path/to/output/dir
"""

import json
import os
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TableStatistics:
    """表格统计分析器"""
    
    def __init__(self, output_dir: str):
        """
        初始化统计分析器
        
        Args:
            output_dir: 输出目录路径，包含annotations、images、metadata三个子文件夹
        """
        self.output_dir = Path(output_dir)
        self.annotations_dir = self.output_dir / "annotations"
        self.images_dir = self.output_dir / "images"
        self.metadata_dir = self.output_dir / "metadata"
        
        # 初始化日志器
        self.logger = logging.getLogger('table_statistics')

        # 验证目录结构
        self._validate_directories()

        # 统计数据存储
        self.stats = {
            'structure': defaultdict(int),
            'content': defaultdict(int),
            'style': defaultdict(int),
            'postprocessing': defaultdict(int),
            'performance': defaultdict(int),
            'files': defaultdict(int)
        }

        # 详细统计数据
        self.detailed_stats = {
            'table_sizes': [],
            'merge_counts': [],
            'font_sizes': [],
            'margin_values': [],
            'file_sizes': []
        }
        
    def _validate_directories(self):
        """验证输出目录结构"""
        required_dirs = [self.annotations_dir, self.images_dir, self.metadata_dir]
        for dir_path in required_dirs:
            if not dir_path.exists():
                raise FileNotFoundError(f"必需的目录不存在: {dir_path}")
        
        if hasattr(self, 'logger'):
            self.logger.info(f"验证目录结构完成: {self.output_dir}")
    
    def analyze_all(self) -> Dict[str, Any]:
        """
        执行完整的统计分析
        
        Returns:
            包含所有统计结果的字典
        """
        if hasattr(self, 'logger'):
            self.logger.info("开始统计分析...")

        # 获取所有样本文件
        metadata_files = list(self.metadata_dir.glob("*.json"))
        total_samples = len(metadata_files)

        if total_samples == 0:
            raise ValueError("未找到任何metadata文件")

        if hasattr(self, 'logger'):
            self.logger.info(f"找到 {total_samples} 个样本")
        
        # 逐个分析样本
        for i, metadata_file in enumerate(metadata_files):
            if i % 100 == 0:
                if hasattr(self, 'logger'):
                    self.logger.info(f"处理进度: {i}/{total_samples}")
                else:
                    print(f"处理进度: {i}/{total_samples}")
            
            try:
                self._analyze_sample(metadata_file)
            except Exception as e:
                # 确保logger存在
                if hasattr(self, 'logger'):
                    self.logger.warning(f"分析样本 {metadata_file.name} 时出错: {e}")
                else:
                    print(f"警告: 分析样本 {metadata_file.name} 时出错: {e}")
                continue
        
        # 生成统计报告
        report = self._generate_report(total_samples)
        
        if hasattr(self, 'logger'):
            self.logger.info("统计分析完成")
        return report
    
    def _analyze_sample(self, metadata_file: Path):
        """分析单个样本"""
        # 读取metadata
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # 读取对应的标注文件
        sample_name = metadata_file.stem
        annotation_file = self.annotations_dir / f"{sample_name}.json"
        
        if annotation_file.exists():
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
        else:
            annotations = None
            if hasattr(self, 'logger'):
                self.logger.warning(f"未找到标注文件: {annotation_file}")
            else:
                print(f"警告: 未找到标注文件: {annotation_file}")
        
        # 获取图像文件大小
        image_file = self.images_dir / f"{sample_name}.png"
        if not image_file.exists():
            image_file = self.images_dir / f"{sample_name}.jpg"
        
        image_size = image_file.stat().st_size if image_file.exists() else 0
        
        # 执行各项统计
        self._analyze_structure(metadata, annotations)
        self._analyze_content(metadata)
        self._analyze_style(metadata)
        self._analyze_postprocessing(metadata)
        self._analyze_performance(metadata)
        self._analyze_files(metadata, image_size)
    
    def _analyze_structure(self, metadata: Dict, annotations: Dict):
        """分析表格结构统计"""
        # 从metadata中获取结构信息
        if 'structure_choices' in metadata:
            structure = metadata['structure_choices']
            
            # 行数统计 (以5为区间)
            if 'body_rows' in structure:
                body_rows = structure['body_rows']['value']
                self.detailed_stats['table_sizes'].append({
                    'body_rows': body_rows,
                    'header_rows': structure.get('header_rows', {}).get('value', 1),
                    'cols': structure.get('cols', {}).get('value', 3)
                })
                
                # 按区间统计行数
                row_range = self._get_range_category(body_rows, 5, start=2)
                self.stats['structure'][f'body_rows_{row_range}'] += 1
            
            # 列数统计
            if 'cols' in structure:
                cols = structure['cols']['value']
                col_range = self._get_range_category(cols, 5, start=2)
                self.stats['structure'][f'cols_{col_range}'] += 1
            
            # 表头行数统计
            if 'header_rows' in structure:
                header_rows = structure['header_rows']['value']
                self.stats['structure'][f'header_rows_{header_rows}'] += 1
        
        # 从annotations中分析合并单元格
        if annotations and 'cells' in annotations:
            merge_count = 0
            max_row_span = 1
            max_col_span = 1
            
            for cell in annotations['cells']:
                if 'rowspan' in cell and cell['rowspan'] > 1:
                    merge_count += 1
                    max_row_span = max(max_row_span, cell['rowspan'])
                
                if 'colspan' in cell and cell['colspan'] > 1:
                    merge_count += 1
                    max_col_span = max(max_col_span, cell['colspan'])
            
            # 合并单元格数量统计
            merge_range = self._get_range_category(merge_count, 5, start=0)
            self.stats['structure'][f'merge_count_{merge_range}'] += 1
            
            # 跨度统计
            if max_row_span > 1:
                self.stats['structure'][f'max_row_span_{max_row_span}'] += 1
            if max_col_span > 1:
                self.stats['structure'][f'max_col_span_{max_col_span}'] += 1
            
            self.detailed_stats['merge_counts'].append(merge_count)
    
    def _analyze_content(self, metadata: Dict):
        """分析内容来源统计"""
        # CSV数据源分布
        if 'file_choices' in metadata and 'csv' in metadata['file_choices']:
            csv_info = metadata['file_choices']['csv']
            directory = csv_info.get('directory', '')
            
            if 'nl2sql_train' in directory:
                self.stats['content']['csv_source_nl2sql'] += 1
            elif 'wikisql_train' in directory:
                self.stats['content']['csv_source_wikisql'] += 1
            else:
                self.stats['content']['csv_source_other'] += 1
        
        # CSV采样信息
        if 'csv_sampling_info' in metadata:
            sampling_info = metadata['csv_sampling_info']
            
            # 采样比例统计
            if 'csv_structure' in sampling_info:
                total_cols = sampling_info['csv_structure'].get('total_columns', 1)
                total_rows = sampling_info['csv_structure'].get('total_data_rows', 1)
                selected_cols = len(sampling_info.get('selected_columns', []))
                selected_rows = len(sampling_info.get('selected_rows', []))
                
                col_ratio = selected_cols / total_cols if total_cols > 0 else 0
                row_ratio = selected_rows / total_rows if total_rows > 0 else 0
                
                # 按比例区间统计
                col_ratio_range = self._get_ratio_category(col_ratio)
                row_ratio_range = self._get_ratio_category(row_ratio)
                
                self.stats['content'][f'col_sampling_ratio_{col_ratio_range}'] += 1
                self.stats['content'][f'row_sampling_ratio_{row_ratio_range}'] += 1
    
    def _analyze_style(self, metadata: Dict):
        """分析样式特征统计"""
        if 'style_choices' not in metadata:
            return
        
        style = metadata['style_choices']
        
        # 字体族分布
        if 'font_family' in style:
            font_family = style['font_family']['value']
            self.stats['style'][f'font_family_{font_family.replace(" ", "_")}'] += 1
        
        # 字体大小分布 (以2px为区间)
        if 'font_size' in style:
            font_size = style['font_size']['value']
            size_range = self._get_range_category(font_size, 2, start=10)
            self.stats['style'][f'font_size_{size_range}'] += 1
            self.detailed_stats['font_sizes'].append(font_size)
        
        # 对齐方式统计
        if 'horizontal_align' in style:
            h_align = style['horizontal_align']['value']
            self.stats['style'][f'horizontal_align_{h_align}'] += 1
        
        if 'vertical_align' in style:
            v_align = style['vertical_align']['value']
            self.stats['style'][f'vertical_align_{v_align}'] += 1
        
        # 样式继承统计
        if 'inheritance_applied' in metadata:
            inheritance = metadata['inheritance_applied']
            for key, changed in inheritance.items():
                if changed:
                    self.stats['style'][f'inheritance_{key}_applied'] += 1
                else:
                    self.stats['style'][f'inheritance_{key}_not_applied'] += 1
    
    def _get_range_category(self, value: int, interval: int, start: int = 0) -> str:
        """获取数值的区间分类"""
        if value < start:
            return f"<{start}"
        
        range_start = ((value - start) // interval) * interval + start
        range_end = range_start + interval - 1
        return f"{range_start}-{range_end}"
    
    def _get_ratio_category(self, ratio: float) -> str:
        """获取比例的分类"""
        if ratio < 0.2:
            return "0-20%"
        elif ratio < 0.4:
            return "20-40%"
        elif ratio < 0.6:
            return "40-60%"
        elif ratio < 0.8:
            return "60-80%"
        else:
            return "80-100%"

    def _analyze_postprocessing(self, metadata: Dict):
        """分析后处理效果统计"""
        if 'postprocessing_choices' not in metadata:
            return

        postprocessing = metadata['postprocessing_choices']

        # 边距控制分布 (以20px为区间)
        if 'margin_control' in postprocessing:
            margin = postprocessing['margin_control']['value']
            margin_range = self._get_range_category(margin, 20, start=30)
            self.stats['postprocessing'][f'margin_{margin_range}'] += 1
            self.detailed_stats['margin_values'].append(margin)

        # 背景图应用统计
        if 'background_applied' in postprocessing:
            if postprocessing['background_applied']:
                self.stats['postprocessing']['background_applied'] += 1
            else:
                self.stats['postprocessing']['background_not_applied'] += 1

        # 透视变换应用统计
        if 'perspective_applied' in postprocessing:
            if postprocessing['perspective_applied']:
                self.stats['postprocessing']['perspective_applied'] += 1
            else:
                self.stats['postprocessing']['perspective_not_applied'] += 1

        # 表格融合统计
        if 'table_blending_enabled' in postprocessing:
            if postprocessing['table_blending_enabled']:
                self.stats['postprocessing']['table_blending_enabled'] += 1
            else:
                self.stats['postprocessing']['table_blending_disabled'] += 1

        # 降质效果统计
        if 'degradation_effects_applied' in postprocessing:
            effects = postprocessing['degradation_effects_applied']
            if isinstance(effects, list):
                effect_count = len(effects)
                self.stats['postprocessing'][f'degradation_effects_{effect_count}'] += 1

                # 统计具体的降质效果类型
                for effect in effects:
                    self.stats['postprocessing'][f'degradation_{effect}'] += 1

        # 背景图目录分布
        if 'file_choices' in metadata and 'background' in metadata['file_choices']:
            bg_info = metadata['file_choices']['background']
            directory = bg_info.get('directory', '')

            if 'pure_white' in directory:
                self.stats['postprocessing']['bg_source_pure_white'] += 1
            elif 'pure' in directory:
                self.stats['postprocessing']['bg_source_pure'] += 1
            elif 'paper' in directory:
                self.stats['postprocessing']['bg_source_paper'] += 1
            elif 'transparent_ink' in directory:
                self.stats['postprocessing']['bg_source_transparent_ink'] += 1
            else:
                self.stats['postprocessing']['bg_source_other'] += 1

    def _analyze_performance(self, metadata: Dict):
        """分析性能相关统计"""
        # TurboJPEG使用统计
        if 'turbo_jpeg_used' in metadata:
            if metadata['turbo_jpeg_used']:
                self.stats['performance']['turbo_jpeg_used'] += 1
            else:
                self.stats['performance']['turbo_jpeg_not_used'] += 1

        # 性能配置统计
        if 'performance_info' in metadata:
            perf_info = metadata['performance_info']

            # 并行处理统计
            if 'parallel_enabled' in perf_info:
                if perf_info['parallel_enabled']:
                    self.stats['performance']['parallel_enabled'] += 1
                else:
                    self.stats['performance']['parallel_disabled'] += 1

            # 工作线程数统计
            if 'max_workers' in perf_info:
                workers = perf_info['max_workers']
                if workers <= 4:
                    self.stats['performance']['workers_1-4'] += 1
                elif workers <= 8:
                    self.stats['performance']['workers_5-8'] += 1
                elif workers <= 16:
                    self.stats['performance']['workers_9-16'] += 1
                else:
                    self.stats['performance']['workers_16+'] += 1

            # TurboJPEG质量统计
            if 'turbo_jpeg_quality' in perf_info:
                quality = perf_info['turbo_jpeg_quality']
                if quality < 80:
                    self.stats['performance']['jpeg_quality_low'] += 1
                elif quality < 95:
                    self.stats['performance']['jpeg_quality_medium'] += 1
                else:
                    self.stats['performance']['jpeg_quality_high'] += 1

    def _analyze_files(self, metadata: Dict, image_size: int):
        """分析文件相关统计"""
        # 图像文件大小统计 (以100KB为区间)
        if image_size > 0:
            size_kb = image_size / 1024
            if size_kb < 100:
                self.stats['files']['image_size_<100KB'] += 1
            elif size_kb < 200:
                self.stats['files']['image_size_100-200KB'] += 1
            elif size_kb < 500:
                self.stats['files']['image_size_200-500KB'] += 1
            elif size_kb < 1000:
                self.stats['files']['image_size_500KB-1MB'] += 1
            else:
                self.stats['files']['image_size_>1MB'] += 1

            self.detailed_stats['file_sizes'].append(size_kb)

        # 字体文件目录统计
        if 'file_choices' in metadata and 'font' in metadata['file_choices']:
            font_info = metadata['file_choices']['font']
            directory = font_info.get('directory', '')

            if 'common' in directory:
                self.stats['files']['font_source_common'] += 1
            elif 'rare' in directory:
                self.stats['files']['font_source_rare'] += 1
            else:
                self.stats['files']['font_source_other'] += 1

    def _generate_report(self, total_samples: int) -> Dict[str, Any]:
        """生成统计报告"""
        report = {
            'summary': {
                'total_samples': total_samples,
                'analysis_date': self._get_current_time()
            },
            'structure_statistics': self._generate_structure_report(total_samples),
            'content_statistics': self._generate_content_report(total_samples),
            'style_statistics': self._generate_style_report(total_samples),
            'postprocessing_statistics': self._generate_postprocessing_report(total_samples),
            'performance_statistics': self._generate_performance_report(total_samples),
            'file_statistics': self._generate_file_report(total_samples)
        }

        return report

    def _generate_structure_report(self, total_samples: int) -> Dict[str, Any]:
        """生成表格结构统计报告"""
        structure_report = {
            'table_size_distribution': {},
            'merge_statistics': {},
            'span_statistics': {}
        }

        # 行数分布
        row_categories = ['2-6', '7-11', '12-16', '17-21', '22-26', '27-31', '32-36', '37-41']
        for category in row_categories:
            count = self.stats['structure'].get(f'body_rows_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            structure_report['table_size_distribution'][f'body_rows_{category}'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 列数分布
        col_categories = ['2-6', '7-11', '12-16', '17-21']
        for category in col_categories:
            count = self.stats['structure'].get(f'cols_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            structure_report['table_size_distribution'][f'cols_{category}'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 表头行数分布
        for header_rows in [1, 2, 3]:
            count = self.stats['structure'].get(f'header_rows_{header_rows}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            structure_report['table_size_distribution'][f'header_rows_{header_rows}'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 合并单元格统计
        merge_categories = ['0-4', '5-9', '10-14', '15-19', '20-24']
        for category in merge_categories:
            count = self.stats['structure'].get(f'merge_count_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            structure_report['merge_statistics'][f'merge_count_{category}'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        return structure_report

    def _generate_content_report(self, total_samples: int) -> Dict[str, Any]:
        """生成内容来源统计报告"""
        content_report = {
            'csv_source_distribution': {},
            'sampling_ratio_distribution': {}
        }

        # CSV数据源分布
        csv_sources = ['nl2sql', 'wikisql', 'other']
        for source in csv_sources:
            count = self.stats['content'].get(f'csv_source_{source}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            content_report['csv_source_distribution'][source] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 采样比例分布
        ratio_categories = ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%']
        for category in ratio_categories:
            col_count = self.stats['content'].get(f'col_sampling_ratio_{category}', 0)
            row_count = self.stats['content'].get(f'row_sampling_ratio_{category}', 0)

            content_report['sampling_ratio_distribution'][f'col_ratio_{category}'] = {
                'count': col_count,
                'percentage': round((col_count / total_samples) * 100, 2) if total_samples > 0 else 0
            }
            content_report['sampling_ratio_distribution'][f'row_ratio_{category}'] = {
                'count': row_count,
                'percentage': round((row_count / total_samples) * 100, 2) if total_samples > 0 else 0
            }

        return content_report

    def _generate_style_report(self, total_samples: int) -> Dict[str, Any]:
        """生成样式统计报告"""
        style_report = {
            'font_distribution': {},
            'alignment_distribution': {},
            'inheritance_statistics': {}
        }

        # 字体族分布
        font_families = ['Arial', 'Times_New_Roman', 'Helvetica', 'Calibri', 'Verdana']
        for font in font_families:
            count = self.stats['style'].get(f'font_family_{font}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            style_report['font_distribution'][font.replace('_', ' ')] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 字体大小分布
        size_categories = ['10-11', '12-13', '14-15', '16-17', '18-19', '20-21']
        for category in size_categories:
            count = self.stats['style'].get(f'font_size_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            style_report['font_distribution'][f'size_{category}px'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 对齐方式分布
        alignments = ['left', 'center', 'right', 'top', 'middle', 'bottom']
        for align in alignments:
            h_count = self.stats['style'].get(f'horizontal_align_{align}', 0)
            v_count = self.stats['style'].get(f'vertical_align_{align}', 0)

            if h_count > 0:
                style_report['alignment_distribution'][f'horizontal_{align}'] = {
                    'count': h_count,
                    'percentage': round((h_count / total_samples) * 100, 2) if total_samples > 0 else 0
                }
            if v_count > 0:
                style_report['alignment_distribution'][f'vertical_{align}'] = {
                    'count': v_count,
                    'percentage': round((v_count / total_samples) * 100, 2) if total_samples > 0 else 0
                }

        # 样式继承统计
        inheritance_types = ['font_family_changed', 'font_size_changed', 'alignment_changed',
                           'padding_changed', 'text_color_changed', 'background_color_changed']
        for inh_type in inheritance_types:
            applied_count = self.stats['style'].get(f'inheritance_{inh_type}_applied', 0)
            not_applied_count = self.stats['style'].get(f'inheritance_{inh_type}_not_applied', 0)
            total_count = applied_count + not_applied_count

            if total_count > 0:
                style_report['inheritance_statistics'][inh_type] = {
                    'applied': applied_count,
                    'not_applied': not_applied_count,
                    'application_rate': round((applied_count / total_count) * 100, 2)
                }

        return style_report

    def _generate_postprocessing_report(self, total_samples: int) -> Dict[str, Any]:
        """生成后处理统计报告"""
        postprocessing_report = {
            'margin_distribution': {},
            'effect_application': {},
            'background_distribution': {}
        }

        # 边距分布
        margin_categories = ['30-49', '50-69', '70-89', '90-109', '110-129']
        for category in margin_categories:
            count = self.stats['postprocessing'].get(f'margin_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            postprocessing_report['margin_distribution'][f'{category}px'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 效果应用统计
        effects = ['background_applied', 'perspective_applied', 'table_blending_enabled']
        for effect in effects:
            applied_count = self.stats['postprocessing'].get(f'{effect}', 0)
            not_applied_count = self.stats['postprocessing'].get(f'{effect.replace("_applied", "_not_applied").replace("_enabled", "_disabled")}', 0)
            total_count = applied_count + not_applied_count

            if total_count > 0:
                postprocessing_report['effect_application'][effect] = {
                    'applied': applied_count,
                    'not_applied': not_applied_count,
                    'application_rate': round((applied_count / total_count) * 100, 2)
                }

        # 背景图目录分布
        bg_sources = ['pure_white', 'pure', 'paper', 'transparent_ink', 'other']
        for source in bg_sources:
            count = self.stats['postprocessing'].get(f'bg_source_{source}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            postprocessing_report['background_distribution'][source] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        return postprocessing_report

    def _generate_performance_report(self, total_samples: int) -> Dict[str, Any]:
        """生成性能统计报告"""
        performance_report = {
            'turbo_jpeg_usage': {},
            'parallel_processing': {},
            'worker_distribution': {},
            'quality_distribution': {}
        }

        # TurboJPEG使用统计
        turbo_used = self.stats['performance'].get('turbo_jpeg_used', 0)
        turbo_not_used = self.stats['performance'].get('turbo_jpeg_not_used', 0)
        total_turbo = turbo_used + turbo_not_used

        if total_turbo > 0:
            performance_report['turbo_jpeg_usage'] = {
                'used': turbo_used,
                'not_used': turbo_not_used,
                'usage_rate': round((turbo_used / total_turbo) * 100, 2)
            }

        # 并行处理统计
        parallel_enabled = self.stats['performance'].get('parallel_enabled', 0)
        parallel_disabled = self.stats['performance'].get('parallel_disabled', 0)
        total_parallel = parallel_enabled + parallel_disabled

        if total_parallel > 0:
            performance_report['parallel_processing'] = {
                'enabled': parallel_enabled,
                'disabled': parallel_disabled,
                'enabled_rate': round((parallel_enabled / total_parallel) * 100, 2)
            }

        # 工作线程数分布
        worker_categories = ['1-4', '5-8', '9-16', '16+']
        for category in worker_categories:
            count = self.stats['performance'].get(f'workers_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            performance_report['worker_distribution'][f'{category}_workers'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        return performance_report

    def _generate_file_report(self, total_samples: int) -> Dict[str, Any]:
        """生成文件统计报告"""
        file_report = {
            'image_size_distribution': {},
            'font_source_distribution': {}
        }

        # 图像文件大小分布
        size_categories = ['<100KB', '100-200KB', '200-500KB', '500KB-1MB', '>1MB']
        for category in size_categories:
            count = self.stats['files'].get(f'image_size_{category}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            file_report['image_size_distribution'][category] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        # 字体来源分布
        font_sources = ['common', 'rare', 'other']
        for source in font_sources:
            count = self.stats['files'].get(f'font_source_{source}', 0)
            percentage = (count / total_samples) * 100 if total_samples > 0 else 0
            file_report['font_source_distribution'][source] = {
                'count': count,
                'percentage': round(percentage, 2)
            }

        return file_report

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def save_report(self, report: Dict[str, Any], output_file: str):
        """保存统计报告到文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        if hasattr(self, 'logger'):
            self.logger.info(f"统计报告已保存到: {output_file}")
        else:
            print(f"统计报告已保存到: {output_file}")

    def print_summary(self, report: Dict[str, Any]):
        """打印统计摘要"""
        print("\n" + "="*60)
        print("TableRender 统计分析报告")
        print("="*60)

        summary = report['summary']
        print(f"总样本数: {summary['total_samples']}")
        print(f"分析时间: {summary['analysis_date']}")

        # 打印主要统计信息
        print("\n主要统计信息:")
        print("-" * 40)

        # 表格尺寸统计
        structure = report['structure_statistics']['table_size_distribution']
        print("表格行数分布:")
        for key, value in structure.items():
            if 'body_rows' in key:
                print(f"  {key}: {value['count']} ({value['percentage']}%)")

        print("\n表格列数分布:")
        for key, value in structure.items():
            if 'cols' in key:
                print(f"  {key}: {value['count']} ({value['percentage']}%)")

        # CSV数据源分布
        content = report['content_statistics']['csv_source_distribution']
        print("\nCSV数据源分布:")
        for source, value in content.items():
            print(f"  {source}: {value['count']} ({value['percentage']}%)")

        # 字体分布
        style = report['style_statistics']['font_distribution']
        print("\n字体族分布:")
        for font, value in style.items():
            if not font.startswith('size_'):
                print(f"  {font}: {value['count']} ({value['percentage']}%)")

        print("\n" + "="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="TableRender统计分析脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
    python tests/table_statistics.py ./output
    python tests/table_statistics.py ./output --report-file stats_report.json
    python tests/table_statistics.py ./output --verbose
        """
    )

    parser.add_argument(
        'output_dir',
        help='输出目录路径，包含annotations、images、metadata三个子文件夹'
    )

    parser.add_argument(
        '--report-file', '-r',
        default='table_statistics_report.json',
        help='统计报告输出文件名 (默认: table_statistics_report.json)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志信息'
    )

    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='只显示错误信息'
    )

    args = parser.parse_args()

    # 配置日志
    log_level = logging.WARNING if args.quiet else (logging.DEBUG if args.verbose else logging.INFO)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    logger = logging.getLogger('table_statistics')

    try:
        # 验证输出目录
        if not os.path.exists(args.output_dir):
            logger.error(f"输出目录不存在: {args.output_dir}")
            sys.exit(1)

        # 创建统计分析器
        analyzer = TableStatistics(args.output_dir)

        # 执行统计分析
        logger.info("开始统计分析...")
        report = analyzer.analyze_all()

        # 保存报告
        analyzer.save_report(report, args.report_file)

        # 打印摘要
        if not args.quiet:
            analyzer.print_summary(report)

        logger.info("统计分析完成")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"统计分析失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
