#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试table_statistics.py脚本的功能
创建模拟数据并验证统计功能
"""

import json
import os
import tempfile
import shutil
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.table_statistics import TableStatistics


def create_mock_data(output_dir: Path, num_samples: int = 10):
    """创建模拟的测试数据"""
    
    # 创建目录结构
    annotations_dir = output_dir / "annotations"
    images_dir = output_dir / "images"
    metadata_dir = output_dir / "metadata"
    
    for dir_path in [annotations_dir, images_dir, metadata_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    for i in range(num_samples):
        sample_name = f"{i:06d}"
        
        # 创建模拟的metadata
        metadata = {
            "sample_seed": 1000000 + i,
            "sample_index": i,
            "generation_timestamp": 1722345678.123 + i,
            "turbo_jpeg_used": i % 2 == 0,
            
            "structure_choices": {
                "header_rows": {"value": 1 + (i % 3), "range": [1, 1], "probability": 0.6},
                "body_rows": {"value": 5 + (i % 10), "range": [5, 10], "probability": 0.25},
                "cols": {"value": 3 + (i % 5), "range": [3, 7], "probability": 0.45},
                "merge_probability": {"value": 0.1 + (i % 3) * 0.05, "range": [0.1, 0.15], "probability": 0.3}
            },
            
            "style_choices": {
                "font_family": {"value": ["Arial", "Times New Roman", "Helvetica"][i % 3], "probability": 0.4},
                "font_size": {"value": 12 + (i % 4), "range": [12, 15], "probability": 0.4},
                "horizontal_align": {"value": ["left", "center", "right"][i % 3], "probability": 0.6},
                "vertical_align": {"value": ["top", "middle", "bottom"][i % 3], "probability": 0.5}
            },
            
            "file_choices": {
                "csv": {
                    "file": f"assets/corpus/{'nl2sql_train' if i % 2 == 0 else 'wikisql_train'}/test_{i}.csv",
                    "directory": f"assets/corpus/{'nl2sql_train' if i % 2 == 0 else 'wikisql_train'}/",
                    "probability": 0.5
                },
                "font": {
                    "file": f"/path/to/font/{'common' if i % 4 != 0 else 'rare'}/font_{i}.ttf",
                    "directory": f"/path/to/font/{'common' if i % 4 != 0 else 'rare'}/",
                    "probability": 0.8 if i % 4 != 0 else 0.2
                },
                "background": {
                    "file": f"/path/to/bg/{'pure_white' if i % 4 == 0 else 'pure'}/bg_{i}.jpg",
                    "directory": f"/path/to/bg/{'pure_white' if i % 4 == 0 else 'pure'}/",
                    "probability": 0.5
                }
            },
            
            "postprocessing_choices": {
                "margin_control": {"value": 60 + (i % 4) * 20, "range": [60, 80], "probability": 0.3},
                "background_applied": i % 3 != 0,
                "perspective_applied": i % 5 == 0,
                "table_blending_enabled": i % 2 == 0,
                "degradation_effects_applied": ["blur"] if i % 6 == 0 else []
            },
            
            "performance_info": {
                "parallel_enabled": i % 2 == 0,
                "max_workers": 4 + (i % 3) * 4,
                "turbo_jpeg_enabled": i % 2 == 0,
                "turbo_jpeg_quality": 90 + (i % 2) * 10,
                "turbo_jpeg_format": "png"
            },
            
            "inheritance_applied": {
                "font_family_changed": i % 5 == 0,
                "font_size_changed": i % 4 == 0,
                "alignment_changed": i % 6 == 0,
                "padding_changed": i % 3 == 0,
                "text_color_changed": i % 7 == 0,
                "background_color_changed": i % 8 == 0
            },
            
            "csv_sampling_info": {
                "source_file": f"test_{i}.csv",
                "selected_columns": list(range(3 + (i % 5))),
                "selected_rows": list(range(5 + (i % 10))),
                "csv_structure": {
                    "total_columns": 10 + (i % 5),
                    "total_data_rows": 20 + (i % 10)
                },
                "sampling_mode": "random"
            }
        }
        
        # 保存metadata
        metadata_file = metadata_dir / f"{sample_name}.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        # 创建模拟的标注
        annotations = {
            "cells": []
        }
        
        # 添加一些模拟的单元格，包括合并单元格
        rows = metadata["structure_choices"]["body_rows"]["value"] + metadata["structure_choices"]["header_rows"]["value"]
        cols = metadata["structure_choices"]["cols"]["value"]
        
        for row in range(rows):
            for col in range(cols):
                cell = {
                    "row": row,
                    "col": col,
                    "text": f"Cell_{row}_{col}",
                    "bbox": [col * 100, row * 50, (col + 1) * 100, (row + 1) * 50]
                }
                
                # 随机添加一些合并单元格
                if i % 4 == 0 and row == 0 and col < cols - 1:  # 表头合并
                    cell["colspan"] = 2
                elif i % 5 == 0 and col == 0 and row < rows - 1:  # 第一列合并
                    cell["rowspan"] = 2
                
                annotations["cells"].append(cell)
        
        # 保存标注
        annotation_file = annotations_dir / f"{sample_name}.json"
        with open(annotation_file, 'w', encoding='utf-8') as f:
            json.dump(annotations, f, indent=2, ensure_ascii=False)
        
        # 创建模拟的图像文件（空文件，只是为了测试文件大小统计）
        image_file = images_dir / f"{sample_name}.png"
        with open(image_file, 'wb') as f:
            # 写入一些随机数据模拟不同的文件大小
            size_kb = 50 + (i % 10) * 50  # 50KB到500KB
            f.write(b'0' * (size_kb * 1024))


def test_table_statistics():
    """测试统计功能"""
    print("开始测试table_statistics功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = Path(temp_dir) / "test_output"
        
        # 创建模拟数据
        print("创建模拟数据...")
        create_mock_data(output_dir, num_samples=20)
        
        # 创建统计分析器
        print("初始化统计分析器...")
        analyzer = TableStatistics(str(output_dir))
        
        # 执行统计分析
        print("执行统计分析...")
        report = analyzer.analyze_all()
        
        # 验证报告结构
        print("验证报告结构...")
        assert 'summary' in report
        assert 'structure_statistics' in report
        assert 'content_statistics' in report
        assert 'style_statistics' in report
        assert 'postprocessing_statistics' in report
        assert 'performance_statistics' in report
        assert 'file_statistics' in report
        
        # 验证样本数量
        assert report['summary']['total_samples'] == 20
        
        # 验证一些具体的统计结果
        structure_stats = report['structure_statistics']['table_size_distribution']
        assert any('body_rows' in key for key in structure_stats.keys())
        assert any('cols' in key for key in structure_stats.keys())
        
        content_stats = report['content_statistics']['csv_source_distribution']
        assert 'nl2sql' in content_stats
        assert 'wikisql' in content_stats
        
        # 保存测试报告
        report_file = Path(temp_dir) / "test_report.json"
        analyzer.save_report(report, str(report_file))
        
        # 验证报告文件存在
        assert report_file.exists()
        
        # 打印摘要
        print("\n测试报告摘要:")
        analyzer.print_summary(report)
        
        print("\n✅ 所有测试通过！")
        
        # 显示一些关键统计信息
        print(f"\n关键统计信息:")
        print(f"- 总样本数: {report['summary']['total_samples']}")
        print(f"- CSV数据源分布: {content_stats}")
        
        font_stats = report['style_statistics']['font_distribution']
        print(f"- 字体分布: {dict(list(font_stats.items())[:3])}")  # 只显示前3个
        
        performance_stats = report['performance_statistics']
        if 'turbo_jpeg_usage' in performance_stats:
            turbo_stats = performance_stats['turbo_jpeg_usage']
            print(f"- TurboJPEG使用率: {turbo_stats.get('usage_rate', 0)}%")


if __name__ == "__main__":
    test_table_statistics()
