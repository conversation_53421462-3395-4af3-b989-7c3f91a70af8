#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试logger修复是否有效
"""

import logging
import tempfile
import json
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.table_statistics import TableStatistics


def test_logger_fix():
    """测试logger修复"""
    print("测试logger修复...")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建临时目录结构
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = Path(temp_dir) / "test_output"
        
        # 创建必需的子目录
        annotations_dir = output_dir / "annotations"
        images_dir = output_dir / "images"
        metadata_dir = output_dir / "metadata"
        
        for dir_path in [annotations_dir, images_dir, metadata_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建一个简单的测试文件
        test_metadata = {
            "sample_seed": 123456,
            "sample_index": 0,
            "generation_timestamp": 1722345678.123,
            "turbo_jpeg_used": True,
            "structure_choices": {
                "header_rows": {"value": 1, "probability": 0.6},
                "body_rows": {"value": 5, "probability": 0.25},
                "cols": {"value": 3, "probability": 0.45}
            },
            "style_choices": {
                "font_family": {"value": "Arial", "probability": 0.4}
            },
            "file_choices": {
                "csv": {
                    "file": "test.csv",
                    "directory": "test_dir/",
                    "probability": 0.5
                }
            }
        }
        
        # 保存测试metadata
        metadata_file = metadata_dir / "000001.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(test_metadata, f, indent=2)
        
        # 创建对应的图像文件
        image_file = images_dir / "000001.png"
        with open(image_file, 'wb') as f:
            f.write(b'test image data')
        
        # 创建对应的标注文件
        annotation_file = annotations_dir / "000001.json"
        with open(annotation_file, 'w', encoding='utf-8') as f:
            json.dump({"cells": []}, f)
        
        try:
            # 测试TableStatistics初始化
            print("测试TableStatistics初始化...")
            analyzer = TableStatistics(str(output_dir))
            print("✅ 初始化成功")
            
            # 测试logger属性
            print("测试logger属性...")
            assert hasattr(analyzer, 'logger'), "logger属性不存在"
            assert analyzer.logger is not None, "logger为None"
            print("✅ logger属性正常")
            
            # 测试统计分析
            print("测试统计分析...")
            report = analyzer.analyze_all()
            print("✅ 统计分析成功")
            
            # 验证报告
            assert 'summary' in report, "报告缺少summary"
            assert report['summary']['total_samples'] == 1, "样本数量不正确"
            print("✅ 报告验证成功")
            
            print("\n🎉 所有测试通过！logger修复成功！")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True


if __name__ == "__main__":
    success = test_logger_fix()
    sys.exit(0 if success else 1)
