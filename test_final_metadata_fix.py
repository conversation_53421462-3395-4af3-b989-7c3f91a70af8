#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证metadata修复是否完全有效
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_final_fix():
    """最终验证修复效果"""
    print("最终验证metadata修复...")
    
    try:
        # 1. 测试基本导入
        print("1. 测试基本导入...")
        from table_render.config import RenderConfig, StructureConfig, PerformanceConfig
        from table_render.resolver import Resolver
        from table_render.utils.metadata_optimizer import metadata_optimizer
        print("✅ 基本导入成功")
        
        # 2. 测试resolver的performance解析
        print("2. 测试resolver的performance解析...")
        
        # 创建包含performance配置的config
        performance_config = PerformanceConfig(
            enable_parallel=True,
            max_workers=16,
            enable_turbo_jpeg=True,
            turbo_jpeg_quality=100,
            turbo_jpeg_format="png"
        )
        
        structure_config = StructureConfig(
            body_rows=5,
            cols=3,
            header_rows=1
        )
        
        config = RenderConfig(
            structure=structure_config,
            performance=performance_config
        )
        
        resolver = Resolver()
        resolved_params = resolver.resolve(config, seed=12345)
        
        # 验证performance参数是否被正确解析
        if hasattr(resolved_params, 'performance') and resolved_params.performance is not None:
            print("✅ performance参数解析成功")
            perf = resolved_params.performance
            print(f"  - enable_parallel: {perf.enable_parallel}")
            print(f"  - max_workers: {perf.max_workers}")
            print(f"  - enable_turbo_jpeg: {perf.enable_turbo_jpeg}")
            print(f"  - turbo_jpeg_quality: {perf.turbo_jpeg_quality}")
            print(f"  - turbo_jpeg_format: {perf.turbo_jpeg_format}")
            print(f"  - css_stability_enabled: {perf.css_stability_enabled}")
        else:
            print("❌ performance参数解析失败")
            return False
        
        # 3. 测试metadata_optimizer
        print("3. 测试metadata_optimizer...")
        
        # 模拟collected_metadata
        collected_metadata = {
            # 结构选择
            "selected_header_rows": 1,
            "header_rows_range_probability": 0.6,
            "selected_body_rows": 5,
            "body_rows_range_probability": 0.2,
            "selected_cols": 3,
            "cols_range_probability": 0.5,
            
            # 样式选择（这些应该由修复后的resolver生成）
            "selected_font_family": "Arial",
            "font_family_option_probability": 0.4,
            "selected_font_size": 14,
            "font_size_range_probability": 0.4,
            "selected_horizontal_align": "center",
            "horizontal_align_option_probability": 0.6,
            "selected_vertical_align": "middle",
            "vertical_align_option_probability": 0.5,
            
            # 文件选择
            "selected_csv_file": "test.csv",
            "selected_csv_directory": "test_dir/",
            "csv_directory_probability": 0.5,
            "selected_font_file": "/path/to/arial.ttf",
            "selected_font_directory": "/path/to/fonts/",
            "font_directory_probability": 0.8,
            "selected_background_file": "/path/to/bg.jpg",
            "selected_background_directory": "/path/to/bg/",
            "background_directory_probability": 0.5,
            
            # 继承信息
            "inheritance_font_family_changed": False,
            "inheritance_font_size_changed": True,
            "inheritance_alignment_changed": False,
            "inheritance_padding_changed": True,
            "inheritance_text_color_changed": False,
            "inheritance_background_color_changed": False
        }
        
        # 测试优化器
        resolved_params_dict = resolved_params.dict()
        optimized = metadata_optimizer.optimize_metadata(
            resolved_params=resolved_params_dict,
            collected_metadata=collected_metadata,
            csv_sampling_info=None,
            sample_seed=123456,
            sample_index=0,
            generation_timestamp=1234567890.0,
            turbo_jpeg_used=True
        )
        
        # 4. 验证关键字段
        print("4. 验证关键字段...")
        
        # 验证performance_info
        performance_info = optimized.get("performance_info", {})
        if performance_info and "parallel_enabled" in performance_info:
            print("✅ performance_info提取成功")
            print(f"  - parallel_enabled: {performance_info['parallel_enabled']}")
            print(f"  - max_workers: {performance_info['max_workers']}")
            print(f"  - turbo_jpeg_enabled: {performance_info['turbo_jpeg_enabled']}")
        else:
            print("❌ performance_info提取失败")
            print(f"performance_info内容: {performance_info}")
            return False
        
        # 验证style_choices
        style_choices = optimized.get("style_choices", {})
        if style_choices and "font_family" in style_choices:
            print("✅ style_choices提取成功")
            print(f"  - font_family: {style_choices['font_family']}")
            print(f"  - font_size: {style_choices.get('font_size', 'N/A')}")
            print(f"  - horizontal_align: {style_choices.get('horizontal_align', 'N/A')}")
        else:
            print("❌ style_choices提取失败")
            print(f"style_choices内容: {style_choices}")
            return False
        
        # 验证file_choices
        file_choices = optimized.get("file_choices", {})
        if file_choices and "font" in file_choices:
            print("✅ file_choices包含font")
            print(f"  - font file: {file_choices['font']['file']}")
            print(f"  - font directory: {file_choices['font']['directory']}")
        else:
            print("❌ file_choices缺少font")
            print(f"file_choices内容: {file_choices}")
            return False
        
        # 5. 验证完整结构
        print("5. 验证完整结构...")
        required_sections = [
            "sample_seed", "sample_index", "generation_timestamp", "turbo_jpeg_used",
            "structure_choices", "style_choices", "file_choices", 
            "postprocessing_choices", "performance_info", "inheritance_applied",
            "resolved_key_params"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in optimized:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 缺少顶级字段: {missing_sections}")
            return False
        else:
            print("✅ 所有必需的顶级字段都存在")
        
        print("\n🎉 最终验证通过！metadata修复完全成功！")
        
        # 保存测试结果
        import json
        output_path = "final_metadata_test_result.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(optimized, f, indent=2, ensure_ascii=False)
        print(f"测试结果已保存到: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 最终验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_fix()
    sys.exit(0 if success else 1)
