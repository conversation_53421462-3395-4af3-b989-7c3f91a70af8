# TableRender V5.3 背景图一致性修复

## 问题描述

在V5.3的3行3列优化后，仍然出现表格不在图像内容的情况，特别是在生成大尺寸图像（如5788x6000）时，表格被定位到了图像边界之外。

## 根本原因分析

### 问题的核心
**CSS定位计算与实际渲染的坐标系不匹配**，导致表格被定位到了渲染区域之外。

### 具体问题
1. **背景图尺寸不一致**：拼接后的背景图实际尺寸与预期尺寸不符
2. **CSS参数不匹配**：CSS背景参数与实际背景图尺寸不一致
3. **表格位置越界**：表格位置计算没有考虑背景图和裁剪区域的边界限制
4. **视口尺寸错误**：浏览器视口尺寸与CSS参数不匹配

### 问题表现
- 表格Y坐标6425~6667，但图像高度只有6000
- 表格实际位置超出了图像边界
- 生成的标注坐标指向图像外部区域

## 修复方案

### 1. 背景图尺寸验证
在`resolver.py`中添加背景图尺寸验证：

```python
def _verify_tiled_background_size(self, background_path: str, expected_width: int, expected_height: int) -> tuple:
    """验证拼接背景图的实际尺寸与预期是否一致"""
    with Image.open(background_path) as img:
        actual_width, actual_height = img.size
        
    if actual_width != expected_width or actual_height != expected_height:
        self.logger.warning(f"[SIZE_VERIFY] ⚠️ 背景图尺寸不匹配！")
        # 记录详细的差异信息
        
    return actual_width, actual_height
```

### 2. 表格边界检查
确保表格位置在背景图和裁剪区域范围内：

```python
def _ensure_table_within_boundaries(self, table_left: int, table_top: int, 
                                  table_width: int, table_height: int,
                                  bg_width: int, bg_height: int,
                                  crop_width: int, crop_height: int) -> tuple:
    """确保表格位置在背景图和crop区域范围内"""
    
    # 计算各种边界限制
    bg_max_left = max(0, bg_width - table_width - 50)
    bg_max_top = max(0, bg_height - table_height - 50)
    crop_max_left = max(0, crop_width - table_width - 50)
    crop_max_top = max(0, crop_height - table_height - 50)
    
    # 取最严格的限制
    max_left = min(bg_max_left, crop_max_left)
    max_top = min(bg_max_top, crop_max_top)
    
    # 确保表格位置在安全范围内
    safe_left = max(50, min(table_left, max_left))
    safe_top = max(50, min(table_top, max_top))
    
    return safe_left, safe_top
```

### 3. CSS参数一致性验证
在`html_renderer.py`中验证CSS参数与视口的一致性：

```python
def _verify_css_background_consistency(self, background_params, viewport_width: int, viewport_height: int):
    """验证CSS背景参数与视口的一致性"""
    
    # 验证视口与CSS裁剪尺寸的一致性
    if viewport_width != css_crop_width or viewport_height != css_crop_height:
        self.logger.warning(f"[CSS_CONSISTENCY] ⚠️ 视口与CSS裁剪尺寸不一致！")
        
    # 验证背景图实际尺寸与CSS参数的一致性
    with Image.open(background_image_path) as img:
        actual_bg_width, actual_bg_height = img.size
        
    if actual_bg_width != css_background_width or actual_bg_height != css_background_height:
        self.logger.warning(f"[CSS_CONSISTENCY] ⚠️ 背景图实际尺寸与CSS参数不一致！")
```

## 修复效果

### 解决的问题
1. **表格定位异常**：确保表格始终在图像边界内
2. **坐标系不匹配**：统一背景图、CSS参数和视口尺寸
3. **尺寸计算错误**：验证每个环节的尺寸一致性
4. **边界越界**：添加多重边界检查和修正

### 保持的功能
1. **3行3列优化**：保持更大的背景图尺寸
2. **CSS背景质量**：保持高质量的CSS背景渲染
3. **配置兼容性**：不影响现有配置文件
4. **性能优化**：不增加显著的性能开销

## 关键修改点

### 1. Resolver阶段
- **背景图尺寸验证**：`_verify_tiled_background_size()`
- **表格边界检查**：`_ensure_table_within_boundaries()`
- **详细日志记录**：记录所有关键尺寸信息

### 2. HTML渲染器阶段
- **CSS参数验证**：`_verify_css_background_consistency()`
- **视口一致性检查**：确保视口与CSS参数匹配
- **背景图文件验证**：检查文件存在性和尺寸

### 3. 日志增强
- **`[SIZE_VERIFY]`**：背景图尺寸验证日志
- **`[BOUNDARY_FIX]`**：表格位置调整日志
- **`[CSS_CONSISTENCY]`**：CSS参数一致性验证日志

## 使用建议

### 调试模式
启用调试模式查看详细的验证过程：
```bash
python -m table_render.main configs/v5_complete.yaml --num-samples 5 --debug
```

### 关键日志
关注以下日志标识：
- `[SIZE_VERIFY]`：背景图尺寸是否匹配
- `[BOUNDARY_FIX]`：表格位置是否被调整
- `[CSS_CONSISTENCY]`：CSS参数是否一致

### 故障排除
如果仍然出现表格不在图像内的问题：
1. 检查背景图文件是否正确生成
2. 查看尺寸验证日志，确认各环节尺寸一致
3. 检查表格位置是否被边界检查调整
4. 验证CSS参数与实际渲染的匹配性

## 技术细节

### 边界检查逻辑
表格位置必须同时满足：
1. **背景图边界**：`table_left + table_width + 50 <= bg_width`
2. **裁剪区域边界**：`table_left + table_width + 50 <= crop_width`
3. **最小边距**：`table_left >= 50, table_top >= 50`

### 一致性验证点
1. **拼接背景图**：实际尺寸 vs 预期尺寸
2. **CSS参数**：背景图尺寸 vs CSS背景参数
3. **视口设置**：视口尺寸 vs CSS裁剪尺寸
4. **文件存在性**：背景图文件是否存在且可读

### 容错机制
- **尺寸不匹配**：使用实际尺寸，记录警告
- **位置越界**：自动调整到安全位置
- **文件缺失**：记录错误，但不中断处理
- **参数异常**：使用默认值，记录详细信息

---

**TableRender V5.3 - 背景图一致性修复，确保表格始终在图像内**
