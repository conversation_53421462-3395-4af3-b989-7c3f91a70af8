#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入修复是否有效
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有相关的导入"""
    print("测试导入修复...")
    
    try:
        print("1. 测试config导入...")
        from table_render.config import ResolvedPerformanceParams, ResolvedParams
        print("✅ config导入成功")
        
        print("2. 测试resolver导入...")
        from table_render.resolver import Resolver
        print("✅ resolver导入成功")
        
        print("3. 测试metadata_optimizer导入...")
        from table_render.utils.metadata_optimizer import metadata_optimizer
        print("✅ metadata_optimizer导入成功")
        
        print("4. 测试ResolvedPerformanceParams实例化...")
        perf_params = ResolvedPerformanceParams(
            enable_parallel=True,
            max_workers=16,
            enable_turbo_jpeg=True,
            turbo_jpeg_quality=100,
            turbo_jpeg_format="png",
            css_stability_enabled=True
        )
        print("✅ ResolvedPerformanceParams实例化成功")
        print(f"  - enable_parallel: {perf_params.enable_parallel}")
        print(f"  - max_workers: {perf_params.max_workers}")
        
        print("5. 测试Resolver实例化...")
        resolver = Resolver()
        print("✅ Resolver实例化成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
