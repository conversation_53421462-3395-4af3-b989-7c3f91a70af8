{"resolved_params": {"structure": {"body_rows": 8, "cols": 6, "header_rows": 2, "merge_probability": 0.12, "max_row_span": 5, "max_col_span": 2, "complex_header": null}, "content": {"source_type": "csv", "csv_file_path": "assets/corpus/nl2sql_train/0025rows_batch_003.csv", "csv_encoding": "utf-8", "csv_mismatch_strategy": "truncate", "csv_dirs": ["assets/corpus/nl2sql_train/", "assets/corpus/wikisql_train/"], "csv_dir_probabilities": [0.5, 0.5], "sampling_mode": "random", "blank_control": {"trigger_probability": 0.01, "cell_blank_probability": 0.4}, "programmatic_types": ["date", "currency", "percentage"]}, "style": {"overflow_strategy": "wrap", "common": {"font": {"font_dirs": ["/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "/aipdf-mlp/jiacheng/code/text_render/example_data/font/rare"], "font_dir_probabilities": [0.8, 0.2], "default_family": "<PERSON><PERSON>", "default_size": 14, "bold_probability": 0.1, "italic_probability": 0.02, "fallback_font": "Microsoft YaHei"}, "horizontal_align": "center", "vertical_align": "middle", "padding": 5, "randomize_color_probability": 0.2, "randomize_border_color_probability": 0.1, "merged_cell_center_probability": 1.0, "color_contrast": {"min_contrast_ratio": 4.5, "use_soft_colors_probability": 0.1}}, "inheritance": {"font_family_change_probability": 0.2, "font_size_change_probability": 0.2, "alignment_change_probability": 0.1, "padding_change_probability": 0.4, "text_color_change_probability": 0.2, "background_color_change_probability": 0.2}, "border_mode": {"mode": "semi", "semi_config": {"row_line_probability": 0.7, "col_line_probability": 0.6, "outer_frame": true, "header_separator": true}}, "zebra_stripes": 0.1, "sizing": {"default_row_height": "auto", "default_col_width": "auto"}}, "postprocessing": {"degradation_blur": {"probability": 0.05}, "degradation_noise": {"probability": 0.05}, "degradation_fade_global": {"probability": 0.05}, "degradation_fade_local": {"probability": 0.05}, "degradation_uneven_lighting": {"probability": 0.05}, "degradation_jpeg": {"probability": 0.05}, "degradation_darker_brighter": {"probability": 0.05}, "degradation_gamma_correction": {"probability": 0.05}, "table_blending": {"enable_transparency": true, "default_color_transparency": 0.0, "meaningful_color_transparency": 0.7}, "perspective": {"probability": 0.0, "range_list": [[0.01, 0.04], [0.04, 0.07], [0.07, 0.1]], "probability_list": [0.4, 0.4, 0.2], "content_area_shrink_ratio": 0.1, "adaptive_scaling": [[10, 1], [40, 0.1]], "decay_rate": 1.0}, "background": {"background_dirs": ["/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure", "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper", "assets/transparent_ink_bgs/"], "background_dir_probabilities": [0.5, 0.1, 0.1, 0.2], "max_scale_factor": 3.0, "prefer_center_probability": 0.8, "margin_control": {"range_list": [[30, 60], [60, 80], [80, 100], [100, 120]], "probability_list": [0.2, 0.3, 0.3, 0.2]}}}, "output": {"output_dir": "./output/", "label_suffix": "_table_annotation"}, "debug": {"debug_output_dir": "./debug_output"}, "performance": {"enable_parallel": true, "max_workers": 16, "enable_turbo_jpeg": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability": {"enable_validation": true, "enable_stability_check": true, "extra_wait_time": 1.0, "background_wait_time": 1.0}}, "seed": 66}, "original_config": {"structure": {"header_rows": {"range_list": [[1, 1], [2, 2], [3, 3]], "probability_list": [0.6, 0.3, 0.1]}, "body_rows": {"range_list": [[2, 5], [6, 10], [11, 20], [21, 30], [31, 40]], "probability_list": [0.2, 0.25, 0.45, 0.09, 0.01]}, "cols": {"range_list": [[2, 5], [6, 10], [11, 20]], "probability_list": [0.5, 0.45, 0.05]}, "merge_probability": {"range_list": [[0, 0.1], [0.1, 0.15], [0.15, 0.2]], "probability_list": [0.5, 0.3, 0.2]}, "max_row_span": {"option_list": [2, 5, 8], "probability_list": [0.29, 0.7, 0.01]}, "max_col_span": {"option_list": [2, 5, 8], "probability_list": [0.29, 0.7, 0.01]}}, "content": {"source_type": "csv", "csv_source": {"csv_dirs": ["assets/corpus/nl2sql_train/", "assets/corpus/wikisql_train/"], "csv_dir_probabilities": [0.5, 0.5], "sampling_mode": "random", "blank_control": {"trigger_probability": 0.01, "cell_blank_probability": 0.4}, "encoding": "utf-8"}}}, "sample_seed": 1234567890, "sample_index": 0, "generation_timestamp": 1722345678.123, "turbo_jpeg_used": true, "csv_sampling_info": {"source_file": "assets/corpus/nl2sql_train/0025rows_batch_003.csv", "selected_columns": [0, 2, 4, 7, 9, 11], "selected_rows": [1, 3, 5, 8, 12, 15, 18, 22, 25, 28], "csv_structure": {"total_columns": 15, "total_data_rows": 32}, "sampling_mode": "random"}, "selected_csv_file": "assets/corpus/nl2sql_train/0025rows_batch_003.csv", "selected_csv_directory": "assets/corpus/nl2sql_train/", "csv_directory_probability": 0.5, "csv_all_directories": ["assets/corpus/nl2sql_train/", "assets/corpus/wikisql_train/"], "csv_all_directory_probabilities": [0.5, 0.5], "selected_header_rows": 2, "selected_header_rows_range": [2, 2], "header_rows_range_probability": 0.3, "header_rows_all_ranges": [[1, 1], [2, 2], [3, 3]], "header_rows_all_range_probabilities": [0.6, 0.3, 0.1], "selected_body_rows": 8, "selected_body_rows_range": [6, 10], "body_rows_range_probability": 0.25, "body_rows_all_ranges": [[2, 5], [6, 10], [11, 20], [21, 30], [31, 40]], "body_rows_all_range_probabilities": [0.2, 0.25, 0.45, 0.09, 0.01], "selected_cols": 6, "selected_cols_range": [6, 10], "cols_range_probability": 0.45, "cols_all_ranges": [[2, 5], [6, 10], [11, 20]], "cols_all_range_probabilities": [0.5, 0.45, 0.05], "selected_merge_probability": 0.12, "selected_merge_probability_range": [0.1, 0.15], "merge_probability_range_probability": 0.3, "merge_probability_all_ranges": [[0, 0.1], [0.1, 0.15], [0.15, 0.2]], "merge_probability_all_range_probabilities": [0.5, 0.3, 0.2], "selected_max_row_span": 5, "max_row_span_option_probability": 0.7, "max_row_span_all_options": [2, 5, 8], "max_row_span_all_option_probabilities": [0.29, 0.7, 0.01], "selected_max_col_span": 2, "max_col_span_option_probability": 0.29, "max_col_span_all_options": [2, 5, 8], "max_col_span_all_option_probabilities": [0.29, 0.7, 0.01], "selected_font_file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/arial.ttf", "selected_font_directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "font_directory_probability": 0.8, "font_all_directories": ["/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "/aipdf-mlp/jiacheng/code/text_render/example_data/font/rare"], "font_all_directory_probabilities": [0.8, 0.2], "selected_default_family": "<PERSON><PERSON>", "default_family_option_probability": 0.4, "default_family_all_options": ["<PERSON><PERSON>", "Times New Roman", "Helvetica", "Calib<PERSON>", "<PERSON><PERSON><PERSON>"], "default_family_all_option_probabilities": [0.4, 0.25, 0.2, 0.1, 0.05], "selected_default_size": 14, "selected_default_size_range": [10, 15], "default_size_range_probability": 0.4, "default_size_all_ranges": [[10, 15], [16, 20], [21, 30]], "default_size_all_range_probabilities": [0.4, 0.5, 0.1], "selected_horizontal_align": "center", "horizontal_align_option_probability": 0.6, "horizontal_align_all_options": ["left", "center", "right"], "horizontal_align_all_option_probabilities": [0.2, 0.6, 0.2], "selected_vertical_align": "middle", "vertical_align_option_probability": 0.5, "vertical_align_all_options": ["top", "middle", "bottom"], "vertical_align_all_option_probabilities": [0.2, 0.5, 0.2], "selected_padding": 5, "selected_padding_range": [4, 6], "padding_range_probability": 0.4, "padding_all_ranges": [[1, 3], [4, 6], [7, 10]], "padding_all_range_probabilities": [0.3, 0.4, 0.3], "selected_background_file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/bg_001.jpg", "selected_background_directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "background_directory_probability": 0.5, "background_all_directories": ["/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure", "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper", "assets/transparent_ink_bgs/"], "background_all_directory_probabilities": [0.5, 0.1, 0.1, 0.2], "selected_margin_control": 75, "selected_margin_control_range": [60, 80], "margin_control_range_probability": 0.3, "margin_control_all_ranges": [[30, 60], [60, 80], [80, 100], [100, 120]], "margin_control_all_range_probabilities": [0.2, 0.3, 0.3, 0.2], "css_table_left": 2063, "css_table_top": 1245, "css_background_width": 4800, "css_background_height": 3200, "css_crop_width": 1920, "css_crop_height": 1080, "css_bg_offset_x": -1440, "css_bg_offset_y": -1060, "apply_background": true, "css_background_applied": true, "apply_perspective": false, "perspective_offset_ratio": 0.0, "content_area_shrink_ratio": 0.1, "max_scale_factor": 3.0}