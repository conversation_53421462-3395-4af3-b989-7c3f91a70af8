{"sample_seed": 2304330383, "sample_index": 6, "generation_timestamp": 1753881342.568243, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 1, "range": [1, 1], "probability": 0.6}, "body_rows": {"value": 13, "range": [11, 20], "probability": 0.45}, "cols": {"value": 3, "range": [2, 5], "probability": 0.5}, "merge_probability": {"value": 0.15457635938189876, "range": [0.15, 0.2], "probability": 0.2}, "max_row_span": {"value": 2, "probability": 0.29}, "max_col_span": {"value": 2, "probability": 0.29}}, "style_choices": {}, "file_choices": {"csv": {"file": "assets/corpus/nl2sql_train/0005rows_batch_004.csv", "directory": "assets/corpus/nl2sql_train/", "probability": 0.5}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/classic_white_doc_bg.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "probability": 0.5}}, "postprocessing_choices": {"margin_control": {"value": 98, "range": [80, 100], "probability": 0.3}, "degradation_effects_applied": [], "perspective_applied": false, "background_applied": true, "table_blending_enabled": false, "transparency_settings": {}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 2253, "top": 3040}, "background_dimensions": {"width": 5760.0, "height": 7560.0}, "crop_dimensions": {"width": 5760.0, "height": 7560.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 3.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/nl2sql_train/0005rows_batch_004.csv", "selected_columns": [111, 0, 22], "selected_rows": [0, 1, 2, 3, 1, 0, 1, 1, 3, 0, 3, 0, 3], "csv_structure": {"total_columns": 159, "total_data_rows": 4}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 1, "body_rows": 13, "cols": 3}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "full", "zebra_stripes_enabled": false, "transparency_enabled": false, "random_seed": 2304330383}}