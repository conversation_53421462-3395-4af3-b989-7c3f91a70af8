{"sample_seed": 3418985049, "sample_index": 5, "generation_timestamp": 1753879001.973705, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 3, "range": [3, 3], "probability": 0.1}, "body_rows": {"value": 18, "range": [11, 20], "probability": 0.45}, "cols": {"value": 4, "range": [2, 5], "probability": 0.5}, "merge_probability": {"value": 0.14769292279531915, "range": [0.1, 0.15], "probability": 0.3}, "max_row_span": {"value": 2, "probability": 0.29}, "max_col_span": {"value": 5, "probability": 0.7}}, "style_choices": {"font_family": {"value": "<PERSON><PERSON>", "probability": 0.4}, "font_size": {"value": 22, "probability": 0.4}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "bottom", "probability": 0.5}, "padding": {"value": 7, "probability": 0.4}}, "file_choices": {"csv": {"file": "assets/corpus/wikisql_train/0009rows_batch_039.csv", "directory": "assets/corpus/wikisql_train/", "probability": 0.5}, "background": {"file": "assets/transparent_ink_bgs/img07728_bg.jpg", "directory": "assets/transparent_ink_bgs/", "probability": 0.2}}, "postprocessing_choices": {"margin_control": {"value": 82, "range": [80, 100], "probability": 0.3}, "degradation_effects_applied": [], "perspective_applied": false, "background_applied": true, "table_blending_enabled": false}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 958, "top": 2280}, "background_dimensions": {"width": 3840.0, "height": 6720.0}, "crop_dimensions": {"width": 3840.0, "height": 6720.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 3.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/wikisql_train/0009rows_batch_039.csv", "selected_columns": [107, 91, 14, 106], "selected_rows": [0, 1, 2, 3, 4, 5, 6, 7, 3, 5, 4, 7, 3, 2, 1, 1, 2, 4], "csv_structure": {"total_columns": 112, "total_data_rows": 8}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 3, "body_rows": 18, "cols": 4}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "semi", "zebra_stripes_enabled": false, "transparency_enabled": false, "random_seed": 3418985049}}