{"sample_seed": 4163891185, "sample_index": 2, "generation_timestamp": 1753876380.7168727, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 3, "range": [3, 3], "probability": 0.1}, "body_rows": {"value": 3, "range": [2, 5], "probability": 0.2}, "cols": {"value": 3, "range": [2, 5], "probability": 0.5}, "merge_probability": {"value": 0.033768006945399796, "range": [0, 0.1], "probability": 0.5}, "max_row_span": {"value": 2, "probability": 0.29}, "max_col_span": {"value": 5, "probability": 0.7}}, "style_choices": {}, "file_choices": {"csv": {"file": "assets/corpus/wikisql_train/0015rows_batch_040.csv", "directory": "assets/corpus/wikisql_train/", "probability": 0.5}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/classic_white_doc_bg.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "probability": 0.5}}, "postprocessing_choices": {"margin_control": {"value": 39, "range": [30, 60], "probability": 0.2}, "degradation_effects_applied": [], "perspective_applied": false, "background_applied": true}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": true, "padding_changed": true, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 2064, "top": 3058}, "background_dimensions": {"width": 5760.0, "height": 7560.0}, "crop_dimensions": {"width": 5760.0, "height": 7560.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 3.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/wikisql_train/0015rows_batch_040.csv", "selected_columns": [11], "selected_rows": [11, 4, 12, 5], "csv_structure": {"total_columns": 113, "total_data_rows": 14}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 3, "body_rows": 3, "cols": 3}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "none", "zebra_stripes_enabled": false, "transparency_enabled": false, "random_seed": 4163891185}}