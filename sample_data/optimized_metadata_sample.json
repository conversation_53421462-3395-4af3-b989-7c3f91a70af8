{"sample_seed": 1234567890, "sample_index": 0, "generation_timestamp": 1722345678.123, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 2, "range": [2, 2], "probability": 0.3}, "body_rows": {"value": 8, "range": [6, 10], "probability": 0.25}, "cols": {"value": 6, "range": [6, 10], "probability": 0.45}, "merge_probability": {"value": 0.12, "range": [0.1, 0.15], "probability": 0.3}, "max_row_span": {"value": 5, "probability": 0.7}, "max_col_span": {"value": 2, "probability": 0.29}}, "style_choices": {"font_family": {"value": "<PERSON><PERSON>", "probability": 0.4}, "font_size": {"value": 14, "range": [10, 15], "probability": 0.4}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "middle", "probability": 0.5}, "padding": {"value": 5, "range": [4, 6], "probability": 0.4}}, "file_choices": {"csv": {"file": "assets/corpus/nl2sql_train/0025rows_batch_003.csv", "directory": "assets/corpus/nl2sql_train/", "probability": 0.5}, "font": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/arial.ttf", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "probability": 0.8}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/bg_001.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "probability": 0.5}}, "postprocessing_choices": {"margin_control": {"value": 75, "range": [60, 80], "probability": 0.3}, "degradation_effects_applied": [], "perspective_applied": false, "background_applied": true, "table_blending_enabled": true, "transparency_settings": {"default_color_transparency": 0.0, "meaningful_color_transparency": 0.7}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": true, "alignment_changed": false, "padding_changed": true, "text_color_changed": false, "background_color_changed": false}, "csv_sampling_info": {"source_file": "assets/corpus/nl2sql_train/0025rows_batch_003.csv", "selected_columns": [0, 2, 4, 7, 9, 11], "selected_rows": [1, 3, 5, 8, 12, 15, 18, 22, 25, 28], "csv_structure": {"total_columns": 15, "total_data_rows": 32}, "sampling_mode": "random"}, "css_render_info": {"table_position": {"left": 2063, "top": 1245}, "background_dimensions": {"width": 4800, "height": 3200}, "crop_dimensions": {"width": 1920, "height": 1080}, "background_offset": {"x": -1440, "y": -1060}, "scaling_factors": {"max_scale_factor": 3.0, "content_area_shrink_ratio": 0.1}}, "resolved_key_params": {"table_size": {"header_rows": 2, "body_rows": 8, "cols": 6}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "semi", "zebra_stripes_enabled": false, "transparency_enabled": true, "random_seed": 66}}