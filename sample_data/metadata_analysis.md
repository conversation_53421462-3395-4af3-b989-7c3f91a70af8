# TableRender Metadata 信息分析

基于 `v5_complete.yaml` 配置文件模拟的表格生成过程，以下是当前metadata中包含的所有信息类型的详细分析。

## 1. 核心配置信息

### 1.1 resolved_params (已解析的具体参数)
- **用途**: 记录经过概率化选择后的具体参数值
- **特点**: 确定性的、可复现的参数
- **包含内容**: 
  - 表格结构参数 (行数、列数、合并概率等)
  - 内容配置 (CSV路径、编码等)
  - 样式配置 (字体、对齐、颜色等)
  - 后处理配置 (降质效果、背景图等)
  - 性能配置 (并行、TurboJPEG等)

### 1.2 original_config (原始配置)
- **用途**: 保留原始的概率化配置，用于参考和调试
- **特点**: 包含所有概率分布和范围定义
- **冗余性**: 与resolved_params存在大量重复信息

## 2. 生成元信息

### 2.1 基础元信息
```json
{
  "sample_seed": 1234567890,           // 样本随机种子
  "sample_index": 0,                   // 样本索引
  "generation_timestamp": 1722345678.123,  // 生成时间戳
  "turbo_jpeg_used": true              // 是否使用TurboJPEG优化
}
```
- **保留建议**: 全部保留，用于样本追踪和性能分析

## 3. CSV采样信息

### 3.1 csv_sampling_info (结构化采样信息)
```json
{
  "source_file": "assets/corpus/nl2sql_train/0025rows_batch_003.csv",
  "selected_columns": [0, 2, 4, 7, 9, 11],
  "selected_rows": [1, 3, 5, 8, 12, 15, 18, 22, 25, 28],
  "csv_structure": {
    "total_columns": 15,
    "total_data_rows": 32
  },
  "sampling_mode": "random"
}
```
- **保留建议**: 全部保留，对于内容追溯和数据分析非常重要

## 4. 详细选择信息 (metadata_collector收集)

### 4.1 文件选择信息
每种文件选择都包含以下模式：
```json
{
  "selected_[category]_file": "具体文件路径",
  "selected_[category]_directory": "选择的目录",
  "[category]_directory_probability": 0.5,
  "[category]_all_directories": ["所有可选目录"],
  "[category]_all_directory_probabilities": [0.5, 0.5]
}
```

**包含的文件类型**:
- **CSV文件选择**: 数据源文件
- **字体文件选择**: 字体资源文件  
- **背景图选择**: 背景图像文件

**冗余分析**:
- `all_directories` 和 `all_directory_probabilities` 与 `original_config` 中的信息重复
- **建议**: 保留选择结果，删除完整选项列表

### 4.2 概率范围选择信息
每种范围选择都包含以下模式：
```json
{
  "selected_[category]": 具体值,
  "selected_[category]_range": [最小值, 最大值],
  "[category]_range_probability": 0.3,
  "[category]_all_ranges": [所有可选范围],
  "[category]_all_range_probabilities": [对应概率]
}
```

**包含的范围类型**:
- 表格结构: `header_rows`, `body_rows`, `cols`, `merge_probability`
- 样式参数: `default_size`, `padding`
- 后处理参数: `margin_control`

**冗余分析**:
- `all_ranges` 和 `all_range_probabilities` 与配置文件重复
- **建议**: 保留选择结果和选择的范围，删除完整选项列表

### 4.3 选项选择信息
每种选项选择都包含以下模式：
```json
{
  "selected_[category]": "选择的选项",
  "[category]_option_probability": 0.4,
  "[category]_all_options": ["所有可选选项"],
  "[category]_all_option_probabilities": [对应概率]
}
```

**包含的选项类型**:
- 表格结构: `max_row_span`, `max_col_span`
- 字体样式: `default_family`
- 对齐方式: `horizontal_align`, `vertical_align`

**冗余分析**:
- `all_options` 和 `all_option_probabilities` 与配置文件重复
- **建议**: 保留选择结果，删除完整选项列表

## 5. CSS渲染中间值

### 5.1 CSS计算参数
```json
{
  "css_table_left": 2063,
  "css_table_top": 1245,
  "css_background_width": 4800,
  "css_background_height": 3200,
  "css_crop_width": 1920,
  "css_crop_height": 1080,
  "css_bg_offset_x": -1440,
  "css_bg_offset_y": -1060,
  "apply_background": true,
  "css_background_applied": true,
  "apply_perspective": false,
  "perspective_offset_ratio": 0.0,
  "content_area_shrink_ratio": 0.1,
  "max_scale_factor": 3.0
}
```

**用途分析**:
- 主要用于调试CSS渲染问题
- 对于最终用户价值有限
- **建议**: 在debug模式下保留，正常模式下删除

## 6. 信息冗余度分析

### 6.1 高冗余信息 (建议删除)
1. **完整选项列表**: `*_all_directories`, `*_all_ranges`, `*_all_options`
2. **完整概率列表**: `*_all_directory_probabilities`, `*_all_range_probabilities`, `*_all_option_probabilities`
3. **原始配置**: `original_config` (与resolved_params重复)
4. **CSS中间值**: 大部分CSS计算参数 (调试用途)

### 6.2 核心信息 (建议保留)
1. **生成元信息**: `sample_seed`, `sample_index`, `generation_timestamp`
2. **选择结果**: `selected_*` 系列字段
3. **选择概率**: `*_probability` 系列字段
4. **CSV采样信息**: 完整的 `csv_sampling_info`
5. **性能信息**: `turbo_jpeg_used`

## 7. 优化建议

### 7.1 精简版metadata结构
```json
{
  "sample_seed": 1234567890,
  "sample_index": 0,
  "generation_timestamp": 1722345678.123,
  "turbo_jpeg_used": true,
  
  "structure_choices": {
    "header_rows": {"value": 2, "range": [2, 2], "probability": 0.3},
    "body_rows": {"value": 8, "range": [6, 10], "probability": 0.25},
    "cols": {"value": 6, "range": [6, 10], "probability": 0.45},
    "merge_probability": {"value": 0.12, "range": [0.1, 0.15], "probability": 0.3}
  },
  
  "file_choices": {
    "csv": {"file": "path/to/file.csv", "directory": "path/to/dir/", "probability": 0.5},
    "font": {"file": "path/to/font.ttf", "directory": "path/to/fontdir/", "probability": 0.8},
    "background": {"file": "path/to/bg.jpg", "directory": "path/to/bgdir/", "probability": 0.5}
  },
  
  "csv_sampling_info": {
    "source_file": "path/to/file.csv",
    "selected_columns": [0, 2, 4, 7, 9, 11],
    "selected_rows": [1, 3, 5, 8, 12, 15, 18, 22, 25, 28],
    "csv_structure": {"total_columns": 15, "total_data_rows": 32},
    "sampling_mode": "random"
  }
}
```

### 7.2 预计减少的信息量
- **删除冗余信息**: 约60-70%的字段
- **保留核心信息**: 约30-40%的字段
- **文件大小**: 预计减少50-60%

这样的精简既保留了关键的追溯信息，又大大减少了存储空间和信息冗余。
