#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极metadata修复验证
这是最后一次验证，确保所有问题都被解决
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_ultimate_fix():
    """终极验证所有修复"""
    print("🔥 终极metadata修复验证...")
    
    try:
        # 1. 测试基本导入
        print("1. 测试基本导入...")
        from table_render.config import RenderConfig, StructureConfig, PerformanceConfig, CommonStyleConfig, FontConfig
        from table_render.resolver import Resolver
        from table_render.utils.metadata_optimizer import metadata_optimizer
        from table_render.utils.metadata_collector import metadata_collector
        print("✅ 基本导入成功")
        
        # 2. 测试metadata_collector的记录功能
        print("2. 测试metadata_collector记录功能...")
        metadata_collector.clear()
        
        # 模拟记录一些样式选择
        metadata_collector.record_intermediate_value("selected_font_family", "Arial")
        metadata_collector.record_intermediate_value("font_family_option_probability", 0.4)
        metadata_collector.record_intermediate_value("selected_font_size", 14)
        metadata_collector.record_intermediate_value("font_size_range_probability", 0.4)
        metadata_collector.record_intermediate_value("selected_horizontal_align", "center")
        metadata_collector.record_intermediate_value("horizontal_align_option_probability", 0.6)
        metadata_collector.record_intermediate_value("selected_font_file", "/path/to/arial.ttf")
        metadata_collector.record_intermediate_value("selected_font_directory", "/path/to/fonts/")
        metadata_collector.record_intermediate_value("font_directory_probability", 0.8)
        
        collected = metadata_collector.get_metadata()
        print("✅ metadata_collector记录成功")
        print(f"  记录的字段数: {len(collected)}")
        
        # 验证关键字段
        required_fields = [
            "selected_font_family", "font_family_option_probability",
            "selected_font_size", "font_size_range_probability",
            "selected_horizontal_align", "horizontal_align_option_probability",
            "selected_font_file", "selected_font_directory", "font_directory_probability"
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in collected:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ metadata_collector缺少字段: {missing_fields}")
            return False
        else:
            print("✅ metadata_collector包含所有必需字段")
        
        # 3. 测试metadata_optimizer的提取功能
        print("3. 测试metadata_optimizer提取功能...")
        
        # 模拟完整的resolved_params
        resolved_params = {
            "structure": {
                "header_rows": 1,
                "body_rows": 5,
                "cols": 3,
                "merge_probability": 0.1,
                "max_row_span": 3,
                "max_col_span": 2
            },
            "content": {
                "source_type": "csv"
            },
            "style": {
                "overflow_strategy": "wrap",
                "border_mode": {"mode": "semi"},
                "zebra_stripes": 0.1
            },
            "postprocessing": {
                "table_blending": {
                    "enable_transparency": True,
                    "default_color_transparency": 0.0,
                    "meaningful_color_transparency": 0.7
                }
            },
            "performance": {
                "enable_parallel": True,
                "max_workers": 16,
                "enable_turbo_jpeg": True,
                "turbo_jpeg_quality": 100,
                "turbo_jpeg_format": "png",
                "css_stability_enabled": True
            },
            "seed": 12345
        }
        
        # 测试优化器
        optimized = metadata_optimizer.optimize_metadata(
            resolved_params=resolved_params,
            collected_metadata=collected,
            csv_sampling_info=None,
            sample_seed=123456,
            sample_index=0,
            generation_timestamp=1234567890.0,
            turbo_jpeg_used=True
        )
        
        print("✅ metadata_optimizer提取成功")
        
        # 4. 验证所有关键部分
        print("4. 验证所有关键部分...")
        
        # 验证style_choices
        style_choices = optimized.get("style_choices", {})
        if not style_choices:
            print("❌ style_choices为空")
            return False
        
        required_style_fields = ["font_family", "font_size", "horizontal_align"]
        missing_style_fields = []
        for field in required_style_fields:
            if field not in style_choices:
                missing_style_fields.append(field)
        
        if missing_style_fields:
            print(f"❌ style_choices缺少字段: {missing_style_fields}")
            return False
        else:
            print("✅ style_choices包含所有必需字段")
            for field in required_style_fields:
                value = style_choices[field].get("value", "N/A")
                probability = style_choices[field].get("probability", "N/A")
                print(f"  - {field}: {value} (概率: {probability})")
        
        # 验证file_choices
        file_choices = optimized.get("file_choices", {})
        if "font" not in file_choices:
            print("❌ file_choices缺少font")
            return False
        else:
            print("✅ file_choices包含font")
            font_info = file_choices["font"]
            print(f"  - font file: {font_info.get('file', 'N/A')}")
            print(f"  - font directory: {font_info.get('directory', 'N/A')}")
            print(f"  - font probability: {font_info.get('probability', 'N/A')}")
        
        # 验证performance_info
        performance_info = optimized.get("performance_info", {})
        if not performance_info:
            print("❌ performance_info为空")
            return False
        
        required_perf_fields = ["parallel_enabled", "max_workers", "turbo_jpeg_enabled"]
        missing_perf_fields = []
        for field in required_perf_fields:
            if field not in performance_info:
                missing_perf_fields.append(field)
        
        if missing_perf_fields:
            print(f"❌ performance_info缺少字段: {missing_perf_fields}")
            return False
        else:
            print("✅ performance_info包含所有必需字段")
            for field in required_perf_fields:
                value = performance_info.get(field, "N/A")
                print(f"  - {field}: {value}")
        
        # 验证postprocessing_choices
        postprocessing_choices = optimized.get("postprocessing_choices", {})
        if "table_blending_enabled" not in postprocessing_choices:
            print("❌ postprocessing_choices缺少table_blending_enabled")
            return False
        else:
            print("✅ postprocessing_choices包含table_blending_enabled")
            print(f"  - table_blending_enabled: {postprocessing_choices['table_blending_enabled']}")
            if "transparency_settings" in postprocessing_choices:
                print(f"  - transparency_settings: {postprocessing_choices['transparency_settings']}")
        
        # 5. 验证完整结构
        print("5. 验证完整结构...")
        required_sections = [
            "sample_seed", "sample_index", "generation_timestamp", "turbo_jpeg_used",
            "structure_choices", "style_choices", "file_choices", 
            "postprocessing_choices", "performance_info", "inheritance_applied",
            "resolved_key_params"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in optimized:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 缺少顶级字段: {missing_sections}")
            return False
        else:
            print("✅ 所有必需的顶级字段都存在")
        
        # 6. 保存完整的测试结果
        print("6. 保存测试结果...")
        output_path = "ultimate_metadata_test_result.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(optimized, f, indent=2, ensure_ascii=False)
        print(f"✅ 测试结果已保存到: {output_path}")
        
        # 7. 与预期格式对比
        print("7. 与预期格式对比...")
        try:
            with open("sample_data/optimized_metadata_sample.json", 'r', encoding='utf-8') as f:
                expected = json.load(f)
            
            # 检查关键结构是否一致
            for section in required_sections:
                if section in expected:
                    if section not in optimized:
                        print(f"❌ 缺少预期的section: {section}")
                        return False
                    elif section == "style_choices":
                        expected_style_keys = set(expected[section].keys())
                        actual_style_keys = set(optimized[section].keys())
                        if not expected_style_keys.issubset(actual_style_keys):
                            missing_keys = expected_style_keys - actual_style_keys
                            print(f"❌ style_choices缺少预期的键: {missing_keys}")
                            return False
            
            print("✅ 与预期格式对比通过")
            
        except FileNotFoundError:
            print("⚠️  预期格式文件不存在，跳过对比")
        
        print("\n🎉🎉🎉 终极验证完全通过！所有问题都已解决！🎉🎉🎉")
        return True
        
    except Exception as e:
        print(f"❌ 终极验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_ultimate_fix()
    sys.exit(0 if success else 1)
