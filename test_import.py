#!/usr/bin/env python3
"""
测试导入修复后的代码
"""

try:
    print("正在测试导入...")
    from table_render.resolver import Resolver
    print("✓ Resolver导入成功")
    
    from table_render.renderers.html_renderer import HtmlRenderer
    print("✓ HtmlRenderer导入成功")
    
    from table_render import main
    print("✓ main模块导入成功")
    
    print("\n🎉 所有导入测试通过！")
    print("V5.3背景图一致性修复已成功应用")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    exit(1)
except SyntaxError as e:
    print(f"❌ 语法错误: {e}")
    exit(1)
except Exception as e:
    print(f"❌ 其他错误: {e}")
    exit(1)
