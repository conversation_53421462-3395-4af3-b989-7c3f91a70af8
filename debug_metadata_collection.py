#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试metadata收集问题
检查collected_metadata中实际包含的信息
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_collected_metadata():
    """调试collected_metadata内容"""
    
    # 模拟一个简单的metadata收集过程
    from table_render.utils.metadata_collector import metadata_collector
    
    # 清空metadata
    metadata_collector.clear()
    
    # 模拟一些记录操作
    print("模拟记录操作...")
    
    # 记录字体族选择
    metadata_collector.record_option_selection(
        category="font_family",
        selected_option="Arial",
        option_probability=0.4,
        all_options=["Arial", "Times New Roman", "Helvetica"],
        all_probabilities=[0.4, 0.3, 0.3]
    )
    
    # 记录字体大小选择
    metadata_collector.record_range_selection(
        category="font_size",
        selected_value=14,
        selected_range=[12, 16],
        range_probability=0.5
    )
    
    # 记录对齐方式选择
    metadata_collector.record_option_selection(
        category="horizontal_align",
        selected_option="center",
        option_probability=0.6
    )
    
    # 记录字体文件选择
    metadata_collector.record_file_selection(
        category="font",
        selected_file="/path/to/arial.ttf",
        selected_directory="/path/to/fonts/",
        directory_probability=0.8
    )
    
    # 获取收集的metadata
    collected = metadata_collector.get_metadata()
    
    print("收集到的metadata:")
    print(json.dumps(collected, indent=2, ensure_ascii=False))
    
    # 检查期望的字段
    expected_fields = [
        "selected_font_family",
        "font_family_option_probability",
        "selected_font_size", 
        "selected_font_size_range",
        "font_size_range_probability",
        "selected_horizontal_align",
        "horizontal_align_option_probability",
        "selected_font_file",
        "selected_font_directory",
        "font_directory_probability"
    ]
    
    print("\n字段检查:")
    for field in expected_fields:
        if field in collected:
            print(f"✅ {field}: {collected[field]}")
        else:
            print(f"❌ 缺少字段: {field}")
    
    # 测试metadata_optimizer
    print("\n测试metadata_optimizer...")
    from table_render.utils.metadata_optimizer import metadata_optimizer
    
    # 模拟resolved_params
    resolved_params = {
        "performance": {
            "enable_parallel": True,
            "max_workers": 16,
            "enable_turbo_jpeg": True,
            "turbo_jpeg_quality": 100,
            "turbo_jpeg_format": "png",
            "css_stability": {
                "enable_validation": True
            }
        },
        "postprocessing": {
            "table_blending": {
                "enable_transparency": True,
                "default_color_transparency": 0.0,
                "meaningful_color_transparency": 0.7
            }
        }
    }
    
    # 测试优化器
    optimized = metadata_optimizer.optimize_metadata(
        resolved_params=resolved_params,
        collected_metadata=collected,
        csv_sampling_info=None,
        sample_seed=123456,
        sample_index=0,
        generation_timestamp=1234567890.0,
        turbo_jpeg_used=True
    )
    
    print("优化后的metadata:")
    print(json.dumps(optimized, indent=2, ensure_ascii=False))
    
    # 检查关键部分
    print("\n关键部分检查:")
    
    style_choices = optimized.get("style_choices", {})
    print(f"style_choices: {style_choices}")
    if not style_choices:
        print("❌ style_choices为空!")
    else:
        print("✅ style_choices包含数据")
    
    performance_info = optimized.get("performance_info", {})
    print(f"performance_info: {performance_info}")
    if not performance_info:
        print("❌ performance_info为空!")
    else:
        print("✅ performance_info包含数据")
    
    file_choices = optimized.get("file_choices", {})
    print(f"file_choices: {file_choices}")
    if "font" not in file_choices:
        print("❌ file_choices缺少font!")
    else:
        print("✅ file_choices包含font")


if __name__ == "__main__":
    debug_collected_metadata()
